﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
答案： 每个朋友分到1个苹果，剩下1个苹果可以给小明吃,"['答案', '：', '每', '个', '朋友', '分到', '1', '个', '苹果', '，', '剩下', '1', '个', '苹果', '可以', '给', '小明', '吃']","['答案', '：', '每个', '朋友', '分到', '1', '个', '苹果', '，', '剩下', '1', '个', '苹果', '可以', '给', '小明', '吃']","['NN', 'PU', 'DT', 'M', 'NN', 'VV', 'CD', 'M', 'NN', 'PU', 'VV', 'CD', 'M', 'NN', 'VV', 'VV', 'NR', 'VV']","['n', 'w', 'r', 'q', 'n', 'v', 'm', 'q', 'n', 'w', 'v', 'm', 'q', 'n', 'v', 'p', 'nh', 'v']","['n', 'w', 'r', 'q', 'n', 'v', 'm', 'q', 'n', 'w', 'v', 'm', 'q', 'n', 'v', 'v', 'nr', 'v']","[('小明', 'PERSON', 16, 17)]","[('小明', 'nr', 16, 17)]","[('1', 'CARDINAL', 6, 7), ('1', 'CARDINAL', 11, 12)]","[[('每个朋友', 'ARG0', 2, 5), ('分到', 'PRED', 5, 6), ('1个苹果', 'ARG2', 6, 9)], [('剩下', 'PRED', 10, 11), ('1个苹果', 'ARG1', 11, 14)], [('剩下1个苹果', 'ARG1', 10, 14), ('给', 'PRED', 15, 16), ('小明', 'ARG2', 16, 17), ('吃', 'ARG3', 17, 18)]]","[(0, 'root'), (1, 'punct'), (5, 'det'), (3, 'clf'), (6, 'nsubj'), (1, 'vmod'), (8, 'nummod'), (9, 'clf'), (6, 'dobj'), (6, 'punct'), (14, 'rcmod'), (13, 'nummod'), (11, 'range'), (16, 'nsubj'), (16, 'mmod'), (6, 'conj'), (16, 'dobj'), (16, 'dep')]","[[(6, 'Accd')], [(1, 'mPunc')], [(4, 'Sco')], [(5, 'Qp')], [(6, 'Agt')], [(0, 'Root')], [(8, 'Quan')], [(9, 'Qp')], [(6, 'Cont')], [(6, 'mPunc')], [(14, 'rPat')], [(13, 'Quan')], [(14, 'Qp')], [(16, 'Pat')], [(16, 'mMod')], [(17, 'mPrep')], [(18, 'Agt')], [(16, 'eSucc')]]","(TOP
  (IP
    (NP (NN 答案))
    (PU ：)
    (IP
      (IP
        (NP (DP (DT 每) (CLP (M 个))) (NP (NN 朋友)))
        (VP (VV 分到) (NP (QP (CD 1) (CLP (M 个))) (NP (NN 苹果)))))
      (PU ，)
      (IP
        (NP (VV 剩下) (QP (CD 1) (CLP (M 个))) (NP (NN 苹果)))
        (VP (VV 可以) (VP (VV 给) (NP (NR 小明)) (IP (VP (VV 吃)))))))))"
