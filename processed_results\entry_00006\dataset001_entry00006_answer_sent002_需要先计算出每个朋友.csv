﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
需要先计算出每个朋友分到几个苹果,"['需要', '先', '计算', '出', '每', '个', '朋友', '分到', '几', '个', '苹果']","['需要', '先', '计算', '出', '每个', '朋友', '分到', '几', '个', '苹果']","['VV', 'AD', 'VV', 'VV', 'DT', 'M', 'NN', 'VV', 'CD', 'M', 'NN']","['v', 'd', 'v', 'v', 'r', 'q', 'n', 'v', 'm', 'q', 'n']","['v', 'd', 'v', 'v', 'r', 'q', 'n', 'v', 'm', 'q', 'n']",[],[],[],"[[('先', 'ARGM-ADV', 1, 2), ('计算', 'PRED', 2, 3), ('每个朋友分到几个苹果', 'ARG1', 4, 11)], [('每个朋友', 'ARG1', 4, 7), ('分到', 'PRED', 7, 8), ('几个苹果', 'ARG2', 8, 11)]]","[(8, 'mmod'), (8, 'advmod'), (8, 'ccomp'), (3, 'rcomp'), (7, 'det'), (5, 'clf'), (8, 'nsubj'), (0, 'root'), (10, 'nummod'), (11, 'clf'), (8, 'dobj')]","[[(3, 'mMod')], [(3, 'Seq')], [(11, 'rCont')], [(3, 'mDir')], [(6, 'Sco')], [(7, 'Qp')], [(8, 'Agt')], [(0, 'Root'), (3, 'dCont')], [(10, 'Quan')], [(11, 'Qp')], [(8, 'Cont')]]","(TOP
  (IP
    (VP
      (IP (VP (VV 需要) (VP (ADVP (AD 先)) (VP (VRD (VV 计算) (VV 出))))))
      (IP
        (NP (DP (DT 每) (CLP (M 个))) (NP (NN 朋友)))
        (VP (VV 分到) (NP (QP (CD 几) (CLP (M 个))) (NP (NN 苹果))))))))"
