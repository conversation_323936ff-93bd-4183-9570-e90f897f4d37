﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
这个苹果就可以给小明吃,"['这', '个', '苹果', '就', '可以', '给', '小明', '吃']","['这个', '苹果', '就', '可以', '给', '小明', '吃']","['DT', 'M', 'NN', 'AD', 'VV', 'VV', 'NR', 'VV']","['r', 'q', 'n', 'd', 'v', 'v', 'nh', 'v']","['r', 'q', 'n', 'd', 'v', 'v', 'nr', 'v']","[('小明', 'PERSON', 6, 7)]","[('小明', 'nr', 6, 7)]",[],"[[('这个苹果', 'ARG1', 0, 3), ('就', 'ARGM-DIS', 3, 4), ('给', 'PRED', 5, 6), ('小明', 'ARG2', 6, 7)]]","[(3, 'det'), (1, 'clf'), (6, 'nsubj'), (6, 'advmod'), (6, 'mmod'), (0, 'root'), (6, 'dobj'), (6, 'dep')]","[[(2, 'Sco')], [(3, 'Qp')], [(6, 'Pat')], [(6, 'mMod')], [(6, 'mMod')], [(0, 'Root')], [(6, 'Datv')], [(6, 'ePurp')]]","(TOP
  (IP
    (NP (DP (DT 这) (CLP (M 个))) (NP (NN 苹果)))
    (VP
      (ADVP (AD 就))
      (VP (VV 可以) (VP (VV 给) (NP (NR 小明)) (IP (VP (VV 吃))))))))"
