#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
英文数据集翻译脚本
将英文数据集中的每一句话翻译成中文，并按问答形式保存
支持多种翻译服务：Google Translate、百度翻译、有道翻译等
"""

import json
import re
import os
import time
import requests
from typing import Dict, Any, List, Optional
import argparse
from urllib.parse import quote
import hashlib
import random

class TranslationService:
    """翻译服务基类"""
    
    def translate(self, text: str, source_lang: str = 'en', target_lang: str = 'zh') -> str:
        """翻译文本"""
        raise NotImplementedError

class GoogleTranslateService(TranslationService):
    """Google翻译服务（免费版）"""
    
    def __init__(self):
        self.base_url = "https://translate.googleapis.com/translate_a/single"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def translate(self, text: str, source_lang: str = 'en', target_lang: str = 'zh-cn') -> str:
        """使用Google翻译API翻译文本"""
        if not text.strip():
            return text
            
        try:
            params = {
                'client': 'gtx',
                'sl': source_lang,
                'tl': target_lang,
                'dt': 't',
                'q': text
            }
            
            response = self.session.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result and result[0]:
                translated_text = ''.join([item[0] for item in result[0] if item[0]])
                return translated_text
            
        except Exception as e:
            print(f"Google翻译错误: {e}")
            return text
        
        return text

class BaiduTranslateService(TranslationService):
    """百度翻译服务"""
    
    def __init__(self, app_id: str = None, secret_key: str = None):
        self.app_id = app_id
        self.secret_key = secret_key
        self.base_url = "https://fanyi-api.baidu.com/api/trans/vip/translate"
        
    def translate(self, text: str, source_lang: str = 'en', target_lang: str = 'zh') -> str:
        """使用百度翻译API翻译文本"""
        if not self.app_id or not self.secret_key:
            print("百度翻译需要APP ID和密钥")
            return text
            
        if not text.strip():
            return text
            
        try:
            salt = str(random.randint(32768, 65536))
            sign_str = self.app_id + text + salt + self.secret_key
            sign = hashlib.md5(sign_str.encode('utf-8')).hexdigest()
            
            params = {
                'q': text,
                'from': source_lang,
                'to': target_lang,
                'appid': self.app_id,
                'salt': salt,
                'sign': sign
            }
            
            response = requests.get(self.base_url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if 'trans_result' in result:
                return result['trans_result'][0]['dst']
                
        except Exception as e:
            print(f"百度翻译错误: {e}")
            return text
        
        return text

class DatasetTranslator:
    """数据集翻译器"""
    
    def __init__(self, translation_service: TranslationService):
        self.translation_service = translation_service
        self.sentence_pattern = re.compile(r'[.!?]+\s*')
        
    def split_into_sentences(self, text: str) -> List[str]:
        """将文本分割成句子"""
        # 简单的句子分割，基于标点符号
        sentences = self.sentence_pattern.split(text)
        # 过滤空句子并保留标点
        result = []
        for i, sentence in enumerate(sentences):
            sentence = sentence.strip()
            if sentence:
                # 如果不是最后一个句子，添加标点
                if i < len(sentences) - 1:
                    # 查找原文中的标点
                    original_end = text.find(sentence) + len(sentence)
                    if original_end < len(text):
                        punct = text[original_end:original_end+1]
                        if punct in '.!?':
                            sentence += punct
                result.append(sentence)
        return result
    
    def extract_text_content(self, entry: Dict[str, Any]) -> str:
        """从数据条目中提取文本内容"""
        text = entry.get('text', '')
        
        # 移除"Human:"和"Assistant:"标记，只保留实际内容
        lines = text.split('\n')
        content_lines = []
        
        for line in lines:
            line = line.strip()
            if line and not line.startswith(('Human:', 'Assistant:')):
                content_lines.append(line)
        
        return '\n'.join(content_lines)
    
    def translate_entry(self, entry: Dict[str, Any], entry_id: int) -> List[Dict[str, Any]]:
        """翻译单个数据条目，返回多个问答对"""
        content = self.extract_text_content(entry)
        if not content.strip():
            return []
        
        # 分割成句子
        sentences = self.split_into_sentences(content)
        if not sentences:
            return []
        
        translated_entries = []
        
        for i, sentence in enumerate(sentences):
            if len(sentence.strip()) < 5:  # 跳过太短的句子
                continue
                
            try:
                # 翻译句子
                translated_sentence = self.translation_service.translate(sentence)
                
                # 创建问答格式
                qa_entry = {
                    "text": f"Human:\n请将以下英文翻译成中文：\n{sentence}\n\nAssistant:\n{translated_sentence}",
                    "metadata": {
                        "original_entry_id": entry_id,
                        "sentence_id": i + 1,
                        "original_text": sentence,
                        "translated_text": translated_sentence,
                        "translation_type": "sentence_translation"
                    }
                }
                
                translated_entries.append(qa_entry)
                
                # 添加延迟避免API限制
                time.sleep(0.1)
                
            except Exception as e:
                print(f"翻译句子时出错 (条目 {entry_id}, 句子 {i+1}): {e}")
                continue
        
        return translated_entries
    
    def translate_dataset(self, input_file: str, output_file: str, max_entries: int = None) -> Dict[str, int]:
        """翻译整个数据集"""
        stats = {
            'total_entries': 0,
            'processed_entries': 0,
            'generated_qa_pairs': 0,
            'errors': 0
        }
        
        try:
            with open(input_file, 'r', encoding='utf-8') as infile, \
                 open(output_file, 'w', encoding='utf-8') as outfile:
                
                for line_num, line in enumerate(infile, 1):
                    if max_entries and stats['total_entries'] >= max_entries:
                        break
                        
                    line = line.strip()
                    if not line:
                        continue
                    
                    try:
                        # 解析JSON
                        entry = json.loads(line)
                        stats['total_entries'] += 1
                        
                        print(f"正在处理第 {stats['total_entries']} 个条目...")
                        
                        # 翻译条目
                        translated_entries = self.translate_entry(entry, stats['total_entries'])
                        
                        # 写入翻译结果
                        for translated_entry in translated_entries:
                            outfile.write(json.dumps(translated_entry, ensure_ascii=False) + '\n')
                            stats['generated_qa_pairs'] += 1
                        
                        if translated_entries:
                            stats['processed_entries'] += 1
                        
                        # 每处理10个条目打印一次进度
                        if stats['total_entries'] % 10 == 0:
                            print(f"已处理 {stats['total_entries']} 个条目，生成 {stats['generated_qa_pairs']} 个问答对")
                    
                    except json.JSONDecodeError as e:
                        print(f"第 {line_num} 行JSON解析错误: {e}")
                        stats['errors'] += 1
                        continue
                    except Exception as e:
                        print(f"第 {line_num} 行处理错误: {e}")
                        stats['errors'] += 1
                        continue
        
        except FileNotFoundError:
            print(f"错误: 找不到输入文件 {input_file}")
            return stats
        except Exception as e:
            print(f"处理文件时发生错误: {e}")
            return stats
        
        return stats

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='翻译英文数据集')
    parser.add_argument('--input', '-i', default='english_data.jsonl', 
                       help='输入的英文数据集文件 (默认: english_data.jsonl)')
    parser.add_argument('--output', '-o', default='translated_dataset.jsonl',
                       help='输出的翻译数据集文件 (默认: translated_dataset.jsonl)')
    parser.add_argument('--max-entries', '-m', type=int, default=None,
                       help='最大处理条目数 (默认: 处理所有条目)')
    parser.add_argument('--service', '-s', choices=['google', 'baidu'], default='google',
                       help='翻译服务 (默认: google)')
    parser.add_argument('--baidu-app-id', help='百度翻译APP ID')
    parser.add_argument('--baidu-secret-key', help='百度翻译密钥')
    
    args = parser.parse_args()
    
    # 选择翻译服务
    if args.service == 'google':
        translation_service = GoogleTranslateService()
        print("使用Google翻译服务")
    elif args.service == 'baidu':
        translation_service = BaiduTranslateService(args.baidu_app_id, args.baidu_secret_key)
        print("使用百度翻译服务")
    else:
        print("不支持的翻译服务")
        return
    
    # 创建翻译器
    translator = DatasetTranslator(translation_service)
    
    print(f"开始翻译数据集...")
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    if args.max_entries:
        print(f"最大处理条目数: {args.max_entries}")
    
    # 开始翻译
    start_time = time.time()
    stats = translator.translate_dataset(args.input, args.output, args.max_entries)
    end_time = time.time()
    
    # 打印统计信息
    print("\n翻译完成!")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print(f"总条目数: {stats['total_entries']}")
    print(f"成功处理条目数: {stats['processed_entries']}")
    print(f"生成问答对数: {stats['generated_qa_pairs']}")
    print(f"错误数: {stats['errors']}")
    
    if stats['total_entries'] > 0:
        success_rate = stats['processed_entries'] / stats['total_entries'] * 100
        print(f"成功率: {success_rate:.2f}%")
    
    print(f"\n翻译结果已保存到: {args.output}")

if __name__ == "__main__":
    main()
