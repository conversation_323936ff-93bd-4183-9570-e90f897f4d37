﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
并且不利用任何作弊手段来获胜,"['并且', '不', '利用', '任何', '作弊', '手段', '来', '获胜']","['并且', '不', '利用', '任何', '作弊', '手段', '来', '获胜']","['AD', 'AD', 'VV', 'DT', 'NN', 'NN', 'MSP', 'VV']","['c', 'd', 'v', 'r', 'v', 'n', 'v', 'v']","['c', 'd', 'v', 'r', 'vn', 'n', 'v', 'v']",[],[],[],"[[('并且', 'ARGM-DIS', 0, 1), ('不', 'ARGM-ADV', 1, 2), ('利用', 'PRED', 2, 3), ('任何作弊手段', 'ARG1', 3, 6)], [('并且', 'ARGM-DIS', 0, 1), ('不', 'ARGM-ADV', 1, 2), ('获胜', 'PRED', 7, 8)]]","[(3, 'advmod'), (3, 'neg'), (0, 'root'), (6, 'det'), (6, 'nn'), (3, 'dobj'), (8, 'prtmod'), (3, 'conj')]","[[(3, 'mConj')], [(3, 'mNeg')], [(0, 'Root')], [(6, 'Sco')], [(6, 'Desc')], [(3, 'Cont')], [(8, 'mVain')], [(3, 'ePurp')]]","(TOP
  (IP
    (VP
      (ADVP (AD 并且))
      (ADVP (AD 不))
      (VP
        (VP (VV 利用) (NP (DP (DT 任何)) (NP (NN 作弊) (NN 手段))))
        (VP (MSP 来) (VP (VV 获胜)))))))"
