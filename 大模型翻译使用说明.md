# 大模型翻译脚本使用说明

## 🎯 概述

`llm_translate.py` 是一个使用大语言模型进行英文数据集翻译的高级脚本，支持多种主流大模型API，包括：

- **OpenAI GPT系列** (GPT-4, GPT-3.5-turbo等)
- **Anthropic Claude系列** (Claude-3-Opus, Sonnet, Haiku)
- **本地模型** (通过Ollama运行的Qwen2, Llama3, ChatGLM等)

## 🚀 快速开始

### 1. 安装依赖
```bash
pip install requests
```

### 2. 测试配置
```bash
python llm_config.py
```

### 3. 选择模型运行

#### 使用本地模型（推荐新手）
```bash
# 确保Ollama已安装并运行
ollama pull qwen2:7b

# 运行翻译
python llm_translate.py --model-type local --local-model qwen2:7b --max-entries 3
```

#### 使用OpenAI
```bash
export OPENAI_API_KEY="your-openai-api-key"
python llm_translate.py --model-type openai --openai-model gpt-3.5-turbo --max-entries 3
```

#### 使用Claude
```bash
export CLAUDE_API_KEY="your-claude-api-key"
python llm_translate.py --model-type claude --claude-model claude-3-haiku-20240307 --max-entries 3
```

## 📋 详细参数说明

### 基础参数
- `--input, -i`: 输入文件路径 (默认: english_data.jsonl)
- `--output, -o`: 输出文件路径 (默认: llm_translated_dataset.jsonl)
- `--max-entries, -n`: 最大处理条目数 (默认: 5)
- `--model-type`: 模型类型 (openai/claude/local)

### OpenAI参数
- `--openai-key`: OpenAI API密钥 (或设置环境变量 OPENAI_API_KEY)
- `--openai-model`: 模型名称 (默认: gpt-3.5-turbo)
- `--openai-base-url`: API基础URL (用于代理或自部署)

### Claude参数
- `--claude-key`: Claude API密钥 (或设置环境变量 CLAUDE_API_KEY)
- `--claude-model`: 模型名称 (默认: claude-3-haiku-20240307)

### 本地模型参数
- `--local-url`: 本地API地址 (默认: http://localhost:11434)
- `--local-model`: 模型名称 (默认: qwen2:7b)

## 🔧 本地模型设置

### 安装Ollama
```bash
# Linux/macOS
curl -fsSL https://ollama.ai/install.sh | sh

# Windows: 下载安装包
# https://ollama.ai/download/windows
```

### 下载推荐模型
```bash
# 中文友好模型
ollama pull qwen2:7b          # 通义千问2 7B
ollama pull chatglm3:6b       # ChatGLM3 6B

# 英文模型
ollama pull llama3:8b         # Llama 3 8B
ollama pull gemma:7b          # Google Gemma 7B
```

### 启动Ollama服务
```bash
ollama serve
```

## 🎨 翻译质量对比

| 模型类型 | 翻译质量 | 速度 | 成本 | 网络需求 |
|---------|---------|------|------|---------|
| GPT-4 | ⭐⭐⭐⭐⭐ | 慢 | 高 | 需要 |
| GPT-3.5-turbo | ⭐⭐⭐⭐ | 中等 | 中等 | 需要 |
| Claude-3-Opus | ⭐⭐⭐⭐⭐ | 慢 | 高 | 需要 |
| Claude-3-Haiku | ⭐⭐⭐⭐ | 快 | 低 | 需要 |
| Qwen2:7b | ⭐⭐⭐⭐ | 快 | 免费 | 不需要 |
| Llama3:8b | ⭐⭐⭐ | 快 | 免费 | 不需要 |

## 💰 成本估算

### 付费API成本 (每1000个token)
- GPT-4: ~$0.03
- GPT-3.5-turbo: ~$0.002
- Claude-3-Opus: ~$0.015
- Claude-3-Haiku: ~$0.00025

### 成本计算示例
```python
# 使用配置工具估算成本
python llm_config.py
```

## 📊 使用示例

### 示例1: 小规模测试
```bash
python llm_translate.py --model-type local --max-entries 3
```

### 示例2: 高质量翻译
```bash
python llm_translate.py --model-type openai --openai-model gpt-4 --max-entries 5
```

### 示例3: 成本优化
```bash
python llm_translate.py --model-type claude --claude-model claude-3-haiku-20240307 --max-entries 10
```

### 示例4: 自定义配置
```bash
python llm_translate.py \
  --model-type local \
  --local-url http://192.168.1.100:11434 \
  --local-model chatglm3:6b \
  --input my_data.jsonl \
  --output my_result.jsonl \
  --max-entries 20
```

## 📁 输出格式

生成的翻译结果格式：
```json
{
  "text": "Human:\n请将以下英文翻译成中文：\nCreate a Python function to process data.\n\nAssistant:\n创建一个处理数据的Python函数。",
  "metadata": {
    "original_entry_id": 1,
    "sentence_id": 1,
    "original_text": "Create a Python function to process data.",
    "translated_text": "创建一个处理数据的Python函数。",
    "translator_type": "LocalLLMTranslator",
    "source": "llm_translation"
  }
}
```

## 🔍 故障排除

### 问题1: 本地模型连接失败
```
错误: 本地模型翻译错误: Connection refused
```
**解决方案:**
1. 确保Ollama服务正在运行: `ollama serve`
2. 检查模型是否已下载: `ollama list`
3. 验证URL是否正确: `curl http://localhost:11434/api/tags`

### 问题2: API密钥错误
```
错误: OpenAI翻译错误: 401 Unauthorized
```
**解决方案:**
1. 检查API密钥是否正确
2. 确认账户余额充足
3. 验证API权限

### 问题3: 翻译质量不佳
**解决方案:**
1. 尝试更高级的模型 (如GPT-4)
2. 调整提示词 (修改脚本中的system_prompt)
3. 减少单次翻译的文本长度

### 问题4: 速度太慢
**解决方案:**
1. 使用本地模型避免网络延迟
2. 选择更快的模型 (如Claude-3-Haiku)
3. 减少延迟时间 (修改脚本中的sleep时间)

## ⚙️ 高级配置

### 自定义提示词
编辑脚本中的 `system_prompt` 变量来自定义翻译风格：

```python
self.system_prompt = """你是专业的技术文档翻译专家。
要求：
1. 准确翻译技术术语
2. 保持代码不变
3. 使用规范的技术中文
4. 只返回翻译结果"""
```

### 批量处理多个文件
```bash
for file in *.jsonl; do
    python llm_translate.py --input "$file" --output "translated_$file" --max-entries 50
done
```

### 并行处理
可以同时运行多个实例处理不同的文件段：
```bash
# 终端1
python llm_translate.py --input part1.jsonl --max-entries 100

# 终端2  
python llm_translate.py --input part2.jsonl --max-entries 100
```

## 📈 性能优化建议

1. **本地模型**: 使用GPU加速，安装CUDA版本的Ollama
2. **API调用**: 合理设置延迟时间，避免触发限制
3. **批量处理**: 将大文件分割成小块并行处理
4. **缓存机制**: 可以添加翻译缓存避免重复翻译

## 🔄 与其他脚本对比

| 特性 | llm_translate.py | enhanced_translate.py | offline_translate.py |
|------|-----------------|---------------------|-------------------|
| 翻译质量 | 最高 | 中等 | 较低 |
| 成本 | 可能有费用 | 免费 | 免费 |
| 网络需求 | 部分需要 | 可选 | 不需要 |
| 配置复杂度 | 中等 | 简单 | 最简单 |
| 适用场景 | 高质量需求 | 平衡需求 | 快速测试 |

## 🎯 推荐使用场景

- **学习测试**: 使用本地模型 (qwen2:7b)
- **生产环境**: 使用Claude-3-Haiku (性价比高)
- **最高质量**: 使用GPT-4或Claude-3-Opus
- **大批量处理**: 使用本地模型避免API费用
- **网络受限**: 仅使用本地模型
