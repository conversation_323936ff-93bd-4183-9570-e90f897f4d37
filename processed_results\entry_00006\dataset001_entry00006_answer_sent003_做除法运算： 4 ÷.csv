﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
做除法运算： 4 ÷ 3 = 1 … 1 每个朋友分到1个苹果,"['做', '除法', '运算', '：', '4', '÷', '3', '=', '1', '…', '1', '每', '个', '朋友', '分到', '1', '个', '苹果']","['做', '除法', '运算', '：', '4', '÷', '3', '=', '1', '…', '1', '每个', '朋友', '分到', '1', '个', '苹果']","['VV', 'NN', 'NN', 'PU', 'CD', 'PU', 'CD', 'PU', 'CD', 'PU', 'CD', 'DT', 'M', 'NN', 'VV', 'CD', 'M', 'NN']","['v', 'n', 'v', 'w', 'm', 'w', 'm', 'w', 'm', 'w', 'm', 'r', 'q', 'n', 'v', 'm', 'q', 'n']","['v', 'n', 'vn', 'w', 'm', 'w', 'm', 'w', 'm', 'w', 'm', 'r', 'q', 'n', 'v', 'm', 'q', 'n']","[('4', 'INTEGER', 4, 5)]",[],"[('4', 'CARDINAL', 4, 5), ('3', 'CARDINAL', 6, 7), ('1', 'CARDINAL', 8, 9), ('1', 'CARDINAL', 10, 11), ('1', 'CARDINAL', 15, 16)]","[[('做', 'PRED', 0, 1), ('除法运算', 'ARG1', 1, 3)], [('每个朋友', 'ARG0', 11, 14), ('分到', 'PRED', 14, 15), ('1个苹果', 'ARG2', 15, 18)]]","[(0, 'root'), (3, 'nn'), (1, 'dobj'), (1, 'punct'), (11, 'nummod'), (11, 'punct'), (11, 'nummod'), (11, 'punct'), (11, 'nummod'), (11, 'punct'), (15, 'nsubj'), (14, 'det'), (12, 'clf'), (15, 'nsubj'), (1, 'dep'), (17, 'nummod'), (18, 'clf'), (15, 'dobj')]","[[(15, 'dMann')], [(3, 'Cont')], [(1, 'Cont')], [(3, 'mPunc')], [(7, 'eCoo')], [(5, 'mPunc')], [(9, 'eCoo')], [(5, 'mPunc'), (7, 'mPunc'), (9, 'mPunc')], [(11, 'eCoo')], [(11, 'mPunc')], [(0, 'Root')], [(13, 'Sco')], [(14, 'Qp')], [(15, 'Agt')], [(1, 'eSucc')], [(17, 'Quan')], [(18, 'Qp')], [(15, 'Cont')]]","(TOP
  (IP
    (IP (VP (VV 做) (NP (NP (NN 除法)) (NN 运算))))
    (PU ：)
    (IP
      (QP (CD 4) (PU ÷) (CD 3) (PU =) (QP (CD 1)) (PU …) (CD 1))
      (NP (DP (DT 每) (CLP (M 个))) (NP (NN 朋友)))
      (VP (VV 分到) (NP (QP (CD 1) (CLP (M 个))) (NP (NN 苹果)))))))"
