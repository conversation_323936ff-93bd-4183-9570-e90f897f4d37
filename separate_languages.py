#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中英文数据集分离脚本
将JSONL格式的数据集按语言分离为中文和英文两个文件
如果一个条目中既有中文又有英文，则归类为中文条目
"""

import json
import re
import os
from typing import Dict, Any, Tu<PERSON>

def contains_chinese(text: str) -> bool:
    """
    检查文本是否包含中文字符

    Args:
        text: 要检查的文本

    Returns:
        bool: 如果包含中文字符返回True，否则返回False
    """
    # 中文字符的Unicode范围（常用汉字）
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    return bool(chinese_pattern.search(text))

def get_chinese_char_ratio(text: str) -> float:
    """
    计算文本中中文字符的比例

    Args:
        text: 要检查的文本

    Returns:
        float: 中文字符比例 (0.0 - 1.0)
    """
    if not text:
        return 0.0

    # 移除空白字符
    text_no_space = re.sub(r'\s', '', text)
    if not text_no_space:
        return 0.0

    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text_no_space)
    return len(chinese_chars) / len(text_no_space)

def count_english_words(text: str) -> int:
    """
    计算文本中英文单词的数量

    Args:
        text: 要检查的文本

    Returns:
        int: 英文单词数量
    """
    # 匹配英文单词（连续的字母）
    english_words = re.findall(r'\b[a-zA-Z]+\b', text)
    return len(english_words)

def count_chinese_words(text: str) -> int:
    """
    计算文本中中文词汇的数量（简单按字符计算）

    Args:
        text: 要检查的文本

    Returns:
        int: 中文字符数量
    """
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
    return len(chinese_chars)

def classify_entry(entry: Dict[str, Any]) -> str:
    """
    对数据条目进行语言分类

    Args:
        entry: JSON数据条目

    Returns:
        str: 'chinese', 'english', 或 'mixed'
    """
    text_content = entry.get('text', '')

    # 计算中英文词汇数量
    chinese_word_count = count_chinese_words(text_content)
    english_word_count = count_english_words(text_content)

    # 判断是否为混合内容（中英文都至少有3个词）
    has_significant_chinese = chinese_word_count >= 3
    has_significant_english = english_word_count >= 3

    if has_significant_chinese and has_significant_english:
        return 'mixed'
    elif has_significant_chinese or (contains_chinese(text_content) and chinese_word_count > 0):
        return 'chinese'
    else:
        return 'english'

def separate_languages(input_file: str, output_dir: str = '.') -> Tuple[int, int, int, int]:
    """
    分离中英文数据集

    Args:
        input_file: 输入的JSONL文件路径
        output_dir: 输出目录，默认为当前目录

    Returns:
        Tuple[int, int, int, int]: (总条目数, 中文条目数, 英文条目数, 混合条目数)
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 输出文件路径
    chinese_file = os.path.join(output_dir, 'chinese_data.jsonl')
    english_file = os.path.join(output_dir, 'english_data.jsonl')
    mixed_file = os.path.join(output_dir, 'mixed_data.jsonl')

    total_count = 0
    chinese_count = 0
    english_count = 0
    mixed_count = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(chinese_file, 'w', encoding='utf-8') as chinese_out, \
             open(english_file, 'w', encoding='utf-8') as english_out, \
             open(mixed_file, 'w', encoding='utf-8') as mixed_out:

            for line_num, line in enumerate(infile, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    # 解析JSON
                    entry = json.loads(line)
                    total_count += 1

                    # 分类
                    language = classify_entry(entry)

                    if language == 'chinese':
                        chinese_out.write(json.dumps(entry, ensure_ascii=False) + '\n')
                        chinese_count += 1
                    elif language == 'mixed':
                        mixed_out.write(json.dumps(entry, ensure_ascii=False) + '\n')
                        mixed_count += 1
                    else:
                        english_out.write(json.dumps(entry, ensure_ascii=False) + '\n')
                        english_count += 1

                    # 每处理1000条记录打印一次进度
                    if total_count % 1000 == 0:
                        print(f"已处理 {total_count} 条记录...")

                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    continue
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return 0, 0, 0, 0
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return 0, 0, 0, 0

    return total_count, chinese_count, english_count, mixed_count

def test_classification():
    """测试分类函数"""
    test_cases = [
        {"text": "Human:\nCreate a Python script snippet that Updates Low Bathing routine", "expected": "english"},
        {"text": "Human:\n根据给定条件进行推理。\n爱丽丝比鲍勃年长4岁。", "expected": "chinese"},
        {"text": "Human:\nWhat is the best thing someone's ever said to you?", "expected": "english"},
        {"text": "Human:\n鉴别两种风格迥异的艺术品，分析其不同之处。", "expected": "chinese"},
        {"text": "Human:\n请用Python写一个function来calculate the average of numbers in a list", "expected": "mixed"},
        {"text": "Human:\nWrite a Python function to 计算列表中数字的平均值 and return the result", "expected": "mixed"},
    ]

    print("测试分类逻辑:")
    for i, case in enumerate(test_cases):
        result = classify_entry(case)
        status = "✓" if result == case["expected"] else "✗"
        chinese_count = count_chinese_words(case["text"])
        english_count = count_english_words(case["text"])
        print(f"{status} 测试 {i+1}: {result} (中文词数: {chinese_count}, 英文词数: {english_count}) - 预期: {case['expected']}")
    print()

def main():
    """主函数"""
    input_file = '001_00001.jsonl'

    # 先测试分类逻辑
    test_classification()

    print("开始分离中英文数据集...")
    print(f"输入文件: {input_file}")

    total, chinese, english, mixed = separate_languages(input_file)

    print("\n分离完成!")
    print(f"总条目数: {total}")
    print(f"中文条目数: {chinese}")
    print(f"英文条目数: {english}")
    print(f"混合条目数: {mixed}")
    if total > 0:
        print(f"中文比例: {chinese/total*100:.2f}%")
        print(f"英文比例: {english/total*100:.2f}%")
        print(f"混合比例: {mixed/total*100:.2f}%")

    print(f"\n输出文件:")
    print(f"中文数据: chinese_data.jsonl")
    print(f"英文数据: english_data.jsonl")
    print(f"混合数据: mixed_data.jsonl")

if __name__ == "__main__":
    main()
