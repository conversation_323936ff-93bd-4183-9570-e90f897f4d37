#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版英文数据集翻译脚本
支持多种翻译模式：在线API、离线词典、混合模式
"""

import json
import re
import time
import requests
import argparse
from typing import Dict, Any, List, Optional

# 扩展的英中词典
ENHANCED_DICT = {
    # 编程相关术语
    'script': '脚本', 'code': '代码', 'program': '程序', 'function': '函数', 'method': '方法',
    'class': '类', 'object': '对象', 'variable': '变量', 'parameter': '参数', 'argument': '参数',
    'algorithm': '算法', 'data': '数据', 'database': '数据库', 'file': '文件', 'system': '系统',
    'application': '应用程序', 'software': '软件', 'library': '库', 'framework': '框架',
    'interface': '接口', 'api': 'API', 'server': '服务器', 'client': '客户端',
    
    # 动作词汇
    'create': '创建', 'build': '构建', 'develop': '开发', 'write': '编写', 'make': '制作',
    'update': '更新', 'modify': '修改', 'change': '更改', 'add': '添加', 'remove': '删除',
    'delete': '删除', 'insert': '插入', 'find': '查找', 'search': '搜索', 'get': '获取',
    'set': '设置', 'put': '放置', 'take': '获取', 'give': '提供', 'show': '显示',
    'display': '显示', 'print': '打印', 'output': '输出', 'input': '输入', 'read': '读取',
    'load': '加载', 'save': '保存', 'store': '存储', 'process': '处理', 'handle': '处理',
    'manage': '管理', 'control': '控制', 'run': '运行', 'execute': '执行', 'perform': '执行',
    'calculate': '计算', 'compute': '计算', 'analyze': '分析', 'check': '检查', 'test': '测试',
    'validate': '验证', 'verify': '验证', 'implement': '实现', 'solve': '解决',
    
    # 描述词汇
    'new': '新的', 'old': '旧的', 'good': '好的', 'best': '最好的', 'better': '更好的',
    'big': '大的', 'small': '小的', 'large': '大的', 'little': '小的', 'long': '长的',
    'short': '短的', 'high': '高的', 'low': '低的', 'fast': '快的', 'slow': '慢的',
    'easy': '简单的', 'hard': '困难的', 'simple': '简单的', 'complex': '复杂的',
    'basic': '基本的', 'advanced': '高级的', 'important': '重要的', 'useful': '有用的',
    'necessary': '必要的', 'required': '必需的', 'optional': '可选的', 'available': '可用的',
    'complete': '完整的', 'correct': '正确的', 'wrong': '错误的', 'true': '真的',
    'false': '假的', 'valid': '有效的', 'empty': '空的', 'full': '满的',
    
    # 常用词汇
    'question': '问题', 'answer': '答案', 'problem': '问题', 'solution': '解决方案',
    'example': '示例', 'sample': '样本', 'template': '模板', 'pattern': '模式',
    'model': '模型', 'design': '设计', 'structure': '结构', 'format': '格式',
    'user': '用户', 'people': '人们', 'person': '人', 'student': '学生', 'teacher': '老师',
    'work': '工作', 'task': '任务', 'job': '作业', 'project': '项目', 'time': '时间',
    'way': '方式', 'method': '方法', 'approach': '方法', 'technique': '技术',
    'information': '信息', 'knowledge': '知识', 'skill': '技能', 'ability': '能力',
    'experience': '经验', 'practice': '实践', 'training': '培训', 'learning': '学习',
    
    # 连接词和介词
    'and': '和', 'or': '或者', 'but': '但是', 'if': '如果', 'then': '那么',
    'else': '否则', 'when': '当', 'where': '在哪里', 'what': '什么', 'how': '如何',
    'why': '为什么', 'who': '谁', 'which': '哪个', 'that': '那个', 'this': '这个',
    'with': '与', 'from': '从', 'to': '到', 'for': '为了', 'by': '通过',
    'in': '在', 'on': '在...上', 'at': '在', 'of': '的', 'about': '关于',
    'before': '之前', 'after': '之后', 'during': '期间', 'between': '之间',
    'first': '首先', 'second': '其次', 'third': '第三', 'last': '最后',
    'next': '下一个', 'previous': '上一个', 'following': '以下',
}

class TranslationEngine:
    """翻译引擎"""
    
    def __init__(self, mode='offline'):
        self.mode = mode
        self.session = requests.Session() if mode in ['online', 'hybrid'] else None
        if self.session:
            self.session.headers.update({
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            })
    
    def online_translate(self, text: str) -> str:
        """在线翻译"""
        try:
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                'client': 'gtx',
                'sl': 'en',
                'tl': 'zh-cn',
                'dt': 't',
                'q': text
            }
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            result = response.json()
            if result and result[0]:
                translated = ''.join([item[0] for item in result[0] if item[0]])
                return translated.strip()
        except Exception as e:
            print(f"在线翻译失败: {e}")
            return None
        return None
    
    def offline_translate(self, text: str) -> str:
        """离线翻译（改进版）"""
        if not text.strip():
            return text
        
        # 预处理常见短语
        text_lower = text.lower()
        
        # 处理常见编程短语
        programming_phrases = {
            'python script': 'Python脚本',
            'code snippet': '代码片段',
            'for loop': 'for循环',
            'if statement': 'if语句',
            'data structure': '数据结构',
            'machine learning': '机器学习',
            'artificial intelligence': '人工智能',
            'web development': '网页开发',
            'software engineering': '软件工程',
            'computer science': '计算机科学',
        }
        
        for phrase, translation in programming_phrases.items():
            text_lower = text_lower.replace(phrase, translation)
        
        # 处理常见问句
        question_patterns = {
            r'what is (.+)\?': r'什么是\1？',
            r'how to (.+)\?': r'如何\1？',
            r'how can (.+)\?': r'如何能够\1？',
            r'why (.+)\?': r'为什么\1？',
            r'where (.+)\?': r'在哪里\1？',
        }
        
        for pattern, replacement in question_patterns.items():
            text_lower = re.sub(pattern, replacement, text_lower)
        
        # 分词并翻译
        words = re.findall(r'\b\w+\b', text_lower)
        translated_words = []
        
        for word in words:
            if word in ENHANCED_DICT:
                translated_words.append(ENHANCED_DICT[word])
            else:
                # 处理词形变化
                base_word = self._get_base_word(word)
                if base_word in ENHANCED_DICT:
                    translated_words.append(ENHANCED_DICT[base_word])
                else:
                    translated_words.append(word)
        
        # 重构句子
        result = ''.join(translated_words)
        
        # 后处理：添加适当的标点和格式
        result = re.sub(r'([。！？])', r'\1 ', result)
        result = result.strip()
        
        return result if result else text
    
    def _get_base_word(self, word: str) -> str:
        """获取单词的基本形式"""
        # 简单的词形还原
        if word.endswith('ing'):
            return word[:-3]
        elif word.endswith('ed'):
            return word[:-2]
        elif word.endswith('s') and len(word) > 3:
            return word[:-1]
        elif word.endswith('ly'):
            return word[:-2]
        return word
    
    def translate(self, text: str) -> str:
        """主翻译方法"""
        if self.mode == 'online':
            result = self.online_translate(text)
            return result if result else text
        elif self.mode == 'offline':
            return self.offline_translate(text)
        elif self.mode == 'hybrid':
            # 先尝试在线翻译，失败则使用离线翻译
            result = self.online_translate(text)
            if result:
                return result
            else:
                return self.offline_translate(text)
        else:
            return text

def extract_sentences(text: str) -> List[str]:
    """提取句子（改进版）"""
    # 清理文本
    lines = text.split('\n')
    clean_lines = []
    
    for line in lines:
        line = line.strip()
        # 跳过标记行和代码块
        if line and not line.startswith(('Human:', 'Assistant:', '```', '#', '//', '/*')):
            # 移除代码注释
            line = re.sub(r'//.*$', '', line)
            line = re.sub(r'/\*.*?\*/', '', line)
            line = line.strip()
            if line:
                clean_lines.append(line)
    
    content = ' '.join(clean_lines)
    
    # 分割句子
    sentences = re.split(r'[.!?]+\s+', content)
    
    # 过滤和清理句子
    result = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 10:  # 只保留有意义的句子
            # 确保句子以标点结尾
            if not sentence[-1] in '.!?':
                sentence += '。'
            result.append(sentence)
    
    return result

def process_dataset(input_file: str, output_file: str, translator: TranslationEngine, 
                   max_entries: int = None) -> Dict[str, int]:
    """处理数据集"""
    stats = {
        'total_entries': 0,
        'processed_entries': 0,
        'generated_qa_pairs': 0,
        'errors': 0
    }
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                if max_entries and stats['total_entries'] >= max_entries:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                try:
                    entry = json.loads(line)
                    stats['total_entries'] += 1
                    
                    print(f"处理第 {stats['total_entries']} 个条目...")
                    
                    # 提取句子
                    original_text = entry.get('text', '')
                    sentences = extract_sentences(original_text)
                    
                    if not sentences:
                        continue
                    
                    # 翻译每个句子
                    for i, sentence in enumerate(sentences):
                        try:
                            print(f"  翻译句子 {i+1}/{len(sentences)}: {sentence[:50]}...")
                            
                            translated = translator.translate(sentence)
                            
                            if translated and translated != sentence:
                                qa_entry = {
                                    "text": f"Human:\n请将以下英文翻译成中文：\n{sentence}\n\nAssistant:\n{translated}",
                                    "metadata": {
                                        "original_entry_id": stats['total_entries'],
                                        "sentence_id": i + 1,
                                        "original_text": sentence,
                                        "translated_text": translated,
                                        "translation_mode": translator.mode,
                                        "source": "enhanced_translation"
                                    }
                                }
                                
                                outfile.write(json.dumps(qa_entry, ensure_ascii=False) + '\n')
                                stats['generated_qa_pairs'] += 1
                            
                            # 在线模式添加延迟
                            if translator.mode in ['online', 'hybrid']:
                                time.sleep(0.2)
                                
                        except Exception as e:
                            print(f"    翻译句子时出错: {e}")
                            continue
                    
                    if sentences:
                        stats['processed_entries'] += 1
                        print(f"  生成了 {len([s for s in sentences if translator.translate(s) != s])} 个问答对")
                    
                    print()
                
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    stats['errors'] += 1
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    stats['errors'] += 1
                    continue
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return stats
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return stats
    
    return stats

def main():
    parser = argparse.ArgumentParser(description='增强版英文数据集翻译脚本')
    parser.add_argument('--input', '-i', default='english_data.jsonl', help='输入文件')
    parser.add_argument('--output', '-o', default='enhanced_translated_dataset.jsonl', help='输出文件')
    parser.add_argument('--mode', '-m', choices=['online', 'offline', 'hybrid'], 
                       default='offline', help='翻译模式')
    parser.add_argument('--max-entries', '-n', type=int, default=10, help='最大处理条目数')
    
    args = parser.parse_args()
    
    print(f"增强版英文数据集翻译")
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    print(f"翻译模式: {args.mode}")
    print(f"最大处理条目数: {args.max_entries}")
    print()
    
    # 创建翻译引擎
    translator = TranslationEngine(args.mode)
    
    # 处理数据集
    start_time = time.time()
    stats = process_dataset(args.input, args.output, translator, args.max_entries)
    end_time = time.time()
    
    # 显示结果
    print("翻译完成!")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print(f"总条目数: {stats['total_entries']}")
    print(f"成功处理条目数: {stats['processed_entries']}")
    print(f"生成问答对数: {stats['generated_qa_pairs']}")
    print(f"错误数: {stats['errors']}")
    
    if stats['total_entries'] > 0:
        success_rate = stats['processed_entries'] / stats['total_entries'] * 100
        print(f"成功率: {success_rate:.2f}%")
    
    print(f"\n翻译结果已保存到: {args.output}")

if __name__ == "__main__":
    main()
