﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
4. 接受失败：竞争的本质就是有输有赢,"['4', '.', '接受', '失败', '：', '竞争', '的', '本质', '就', '是', '有', '输', '有', '赢']","['4', '.', '接受', '失败', '：', '竞争', '的', '本质', '就是', '有', '输', '有', '赢']","['CD', 'PU', 'VV', 'NN', 'PU', 'NN', 'DEG', 'NN', 'AD', 'VC', 'VE', 'NN', 'VE', 'NN']","['m', 'w', 'v', 'v', 'w', 'v', 'u', 'n', 'd', 'vl', 'v', 'v', 'v', 'v']","['m', 'w', 'v', 'vn', 'w', 'v', 'u', 'n', 'd', 'v', 'v', 'v', 'v', 'vn']",[],[],"[('4', 'CARDINAL', 0, 1)]","[[('4', 'ARGM-DIS', 0, 1), ('接受', 'PRED', 2, 3), ('失败', 'ARG1', 3, 4)], [('竞争的本质', 'ARG0', 5, 8), ('就', 'ARGM-ADV', 8, 9), ('是', 'PRED', 9, 10), ('有输有赢', 'ARG1', 10, 14)], [('有', 'PRED', 10, 11), ('输', 'ARG1', 11, 12)], [('有', 'PRED', 12, 13), ('赢', 'ARG1', 13, 14)]]","[(3, 'dep'), (3, 'punct'), (0, 'root'), (3, 'dobj'), (3, 'punct'), (8, 'assmod'), (6, 'assm'), (10, 'nsubj'), (10, 'advmod'), (3, 'dep'), (10, 'ccomp'), (11, 'dobj'), (11, 'conj'), (13, 'dobj')]","[[(3, 'mConj')], [(1, 'mPunc')], [(0, 'Root')], [(3, 'Cont')], [(3, 'mPunc')], [(8, 'Host')], [(6, 'mAux')], [(10, 'Exp')], [(10, 'mMod')], [(3, 'eSucc')], [(10, 'dClas')], [(10, 'dClas'), (11, 'Belg')], [(11, 'eCoo')], [(13, 'Belg')]]","(TOP
  (IP
    (IP (LST (CD 4)) (PU .) (VP (VV 接受) (NP (NN 失败))))
    (PU ：)
    (IP
      (NP (DNP (NP (NN 竞争)) (DEG 的)) (NP (NN 本质)))
      (VP
        (ADVP (AD 就))
        (VP
          (VC 是)
          (IP (VP (VP (VE 有) (NP (NN 输))) (VP (VE 有) (NP (NN 赢))))))))))"
