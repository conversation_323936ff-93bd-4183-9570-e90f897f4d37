# 英文数据集翻译脚本使用说明

## 概述

本项目提供了多个Python脚本，用于将英文数据集翻译成中文问答对格式：

1. `translate_dataset.py` - 完整功能版本，支持多种翻译服务
2. `simple_translate.py` - 简化版本，使用免费Google翻译API
3. `offline_translate.py` - 离线版本，使用本地词典翻译
4. `enhanced_translate.py` - 增强版本，支持多种模式

## 脚本特点对比

| 脚本名称 | 翻译方式 | 网络需求 | 翻译质量 | 适用场景 |
|---------|---------|---------|---------|---------|
| translate_dataset.py | 在线API | 需要 | 高 | 生产环境 |
| simple_translate.py | Google翻译 | 需要 | 中等 | 快速测试 |
| offline_translate.py | 本地词典 | 不需要 | 低 | 离线环境 |
| enhanced_translate.py | 混合模式 | 可选 | 中高 | 推荐使用 |

## 功能特点

- 自动将英文数据集中的每个条目分割成句子
- 将每个句子翻译成中文
- 生成标准的问答格式：`Human: 请将以下英文翻译成中文：[英文句子] Assistant: [中文翻译]`
- 支持批量处理大型数据集
- 包含错误处理和进度显示
- 生成详细的处理统计信息

## 安装依赖

```bash
pip install requests
```

## 使用方法

### 方法一：使用增强版脚本（推荐）

```bash
# 离线模式（无需网络）
python enhanced_translate.py --mode offline --max-entries 10

# 在线模式（需要网络）
python enhanced_translate.py --mode online --max-entries 10

# 混合模式（优先在线，失败时使用离线）
python enhanced_translate.py --mode hybrid --max-entries 10
```

### 方法二：使用离线版脚本

```bash
python offline_translate.py
```

这个脚本会：
- 读取 `english_data.jsonl` 文件
- 处理前20个条目（可在脚本中修改）
- 输出到 `offline_translated_qa_dataset.jsonl`
- 使用本地词典翻译，无需网络

### 方法三：使用简化版脚本

```bash
python simple_translate.py
```

### 方法四：使用完整版脚本

```bash
# 使用Google翻译（默认）
python translate_dataset.py --input english_data.jsonl --output translated_dataset.jsonl

# 使用百度翻译（需要API密钥）
python translate_dataset.py --service baidu --baidu-app-id YOUR_APP_ID --baidu-secret-key YOUR_SECRET_KEY
```

## 参数说明

### translate_dataset.py 参数

- `--input, -i`: 输入文件路径（默认：english_data.jsonl）
- `--output, -o`: 输出文件路径（默认：translated_dataset.jsonl）
- `--max-entries, -m`: 最大处理条目数（默认：处理所有）
- `--service, -s`: 翻译服务（google/baidu，默认：google）
- `--baidu-app-id`: 百度翻译APP ID
- `--baidu-secret-key`: 百度翻译密钥

## 输出格式

生成的每个问答对格式如下：

```json
{
  "text": "Human:\n请将以下英文翻译成中文：\nCreate a Python script snippet that Updates Low Bathing routine.\n\nAssistant:\n创建一个更新低频洗浴例程的Python脚本片段。",
  "metadata": {
    "original_entry_id": 1,
    "sentence_id": 1,
    "original_text": "Create a Python script snippet that Updates Low Bathing routine.",
    "translated_text": "创建一个更新低频洗浴例程的Python脚本片段。",
    "translation_type": "sentence_translation"
  }
}
```

## 注意事项

### API限制
- Google翻译免费API有请求频率限制，脚本已添加延迟
- 百度翻译需要注册账号获取API密钥
- 建议分批处理大型数据集

### 翻译质量
- 免费API的翻译质量可能不如付费服务
- 建议对重要数据进行人工校对
- 技术术语可能翻译不准确

### 性能优化
- 可以调整延迟时间来平衡速度和API限制
- 大型数据集建议分批处理
- 可以添加缓存机制避免重复翻译

## 自定义配置

### 修改句子分割规则

在脚本中找到 `split_into_sentences` 函数，可以修改正则表达式来改变句子分割规则：

```python
def split_into_sentences(text: str) -> List[str]:
    # 当前使用标点符号分割
    sentences = re.split(r'[.!?]+\s*', text)
    # 可以添加更复杂的分割逻辑
```

### 修改翻译提示词

可以修改问答格式中的提示词：

```python
qa_entry = {
    "text": f"Human:\n请将以下英文翻译成中文：\n{sentence}\n\nAssistant:\n{translated_sentence}",
    # 可以改为其他格式，如：
    # "text": f"Human:\n翻译：{sentence}\n\nAssistant:\n{translated_sentence}",
}
```

## 故障排除

### 常见错误

1. **网络连接错误**
   - 检查网络连接
   - 尝试使用VPN
   - 增加超时时间

2. **API限制错误**
   - 增加延迟时间
   - 减少并发请求
   - 分批处理数据

3. **文件编码错误**
   - 确保输入文件是UTF-8编码
   - 检查文件格式是否正确

### 调试模式

可以在脚本中添加调试信息：

```python
# 在翻译函数中添加
print(f"正在翻译: {text[:50]}...")
print(f"翻译结果: {translated_text[:50]}...")
```

## 扩展功能

### 添加其他翻译服务

可以继承 `TranslationService` 类来添加新的翻译服务：

```python
class MyTranslateService(TranslationService):
    def translate(self, text: str, source_lang: str = 'en', target_lang: str = 'zh') -> str:
        # 实现你的翻译逻辑
        return translated_text
```

### 批量处理多个文件

可以修改主函数来处理多个输入文件：

```python
input_files = ['english_data1.jsonl', 'english_data2.jsonl']
for input_file in input_files:
    # 处理每个文件
```

## 示例输出

运行脚本后，你会看到类似的输出：

```
开始翻译英文数据集...
输入文件: english_data.jsonl
输出文件: translated_qa_dataset.jsonl
最大处理条目数: 50

处理第 1 个条目...
  翻译句子 1/2: Create a Python script snippet that Updates Low...
  翻译句子 2/2: Use if/else or switch/case statements to...
  生成了 2 个问答对

处理第 2 个条目...
  翻译句子 1/1: What is the best thing someone's ever said to you...
  生成了 1 个问答对

翻译完成!
总条目数: 50
成功处理条目数: 48
生成问答对数: 156
错误数: 2
成功率: 96.00%

翻译结果已保存到: translated_qa_dataset.jsonl
```

## 联系支持

如果遇到问题或需要帮助，请检查：
1. Python版本（建议3.7+）
2. 依赖包是否正确安装
3. 输入文件格式是否正确
4. 网络连接是否正常
