#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大模型翻译脚本
支持多种大模型API进行英文数据集翻译
包括OpenAI GPT、Claude、本地模型等
"""

import json
import re
import time
import requests
import argparse
from typing import Dict, Any, List, Optional
import os
from abc import ABC, abstractmethod

class LLMTranslator(ABC):
    """大模型翻译器基类"""
    
    @abstractmethod
    def translate(self, text: str) -> str:
        """翻译文本"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查模型是否可用"""
        pass

class OpenAITranslator(LLMTranslator):
    """OpenAI GPT翻译器"""
    
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo", base_url: str = None):
        self.api_key = api_key
        self.model = model
        self.base_url = base_url or "https://api.openai.com/v1"
        self.session = requests.Session()
        self.session.headers.update({
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        })
        
        # 翻译提示词
        self.system_prompt = """你是一个专业的英中翻译专家。请将用户提供的英文文本翻译成自然流畅的中文。

翻译要求：
1. 保持原文的意思和语气
2. 使用自然流畅的中文表达
3. 对于编程术语，使用常见的中文翻译或保留英文
4. 保持原文的格式和结构
5. 只返回翻译结果，不要添加额外说明

示例：
英文：Create a Python script that processes data
中文：创建一个处理数据的Python脚本"""
    
    def translate(self, text: str) -> str:
        """使用OpenAI API翻译文本"""
        if not text.strip():
            return text
            
        try:
            payload = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": self.system_prompt},
                    {"role": "user", "content": f"请翻译以下英文：\n{text}"}
                ],
                "temperature": 0.3,
                "max_tokens": 1000
            }
            
            response = self.session.post(
                f"{self.base_url}/chat/completions",
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            if 'choices' in result and len(result['choices']) > 0:
                translated = result['choices'][0]['message']['content'].strip()
                return translated
            else:
                print(f"OpenAI API响应格式错误: {result}")
                return text
                
        except Exception as e:
            print(f"OpenAI翻译错误: {e}")
            return text
    
    def is_available(self) -> bool:
        """检查OpenAI API是否可用"""
        try:
            response = self.session.get(f"{self.base_url}/models", timeout=10)
            return response.status_code == 200
        except:
            return False

class ClaudeTranslator(LLMTranslator):
    """Claude翻译器"""
    
    def __init__(self, api_key: str, model: str = "claude-3-haiku-20240307"):
        self.api_key = api_key
        self.model = model
        self.session = requests.Session()
        self.session.headers.update({
            "x-api-key": api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        })
        
        self.system_prompt = """你是一个专业的英中翻译专家。请将英文文本翻译成自然流畅的中文。

要求：
- 保持原意和语气
- 使用地道的中文表达
- 编程术语可保留英文或使用常见中文翻译
- 只返回翻译结果"""
    
    def translate(self, text: str) -> str:
        """使用Claude API翻译文本"""
        if not text.strip():
            return text
            
        try:
            payload = {
                "model": self.model,
                "max_tokens": 1000,
                "temperature": 0.3,
                "system": self.system_prompt,
                "messages": [
                    {"role": "user", "content": f"请翻译：{text}"}
                ]
            }
            
            response = self.session.post(
                "https://api.anthropic.com/v1/messages",
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            
            result = response.json()
            if 'content' in result and len(result['content']) > 0:
                translated = result['content'][0]['text'].strip()
                return translated
            else:
                print(f"Claude API响应格式错误: {result}")
                return text
                
        except Exception as e:
            print(f"Claude翻译错误: {e}")
            return text
    
    def is_available(self) -> bool:
        """检查Claude API是否可用"""
        try:
            # Claude没有简单的健康检查端点，这里简化处理
            return bool(self.api_key)
        except:
            return False

class LocalLLMTranslator(LLMTranslator):
    """本地大模型翻译器（支持Ollama等）"""
    
    def __init__(self, base_url: str = "http://localhost:11434", model: str = "qwen2:7b"):
        self.base_url = base_url.rstrip('/')
        self.model = model
        self.session = requests.Session()
        
        self.system_prompt = """你是专业的英中翻译专家。请将英文翻译成自然流畅的中文。

要求：
1. 保持原意和语气
2. 使用地道中文表达
3. 编程术语可保留英文
4. 只返回翻译结果，不要解释"""
    
    def translate(self, text: str) -> str:
        """使用本地模型翻译文本"""
        if not text.strip():
            return text
            
        try:
            # Ollama API格式
            payload = {
                "model": self.model,
                "prompt": f"{self.system_prompt}\n\n请翻译以下英文：\n{text}\n\n中文翻译：",
                "stream": False,
                "options": {
                    "temperature": 0.3,
                    "top_p": 0.9
                }
            }
            
            response = self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=60
            )
            response.raise_for_status()
            
            result = response.json()
            if 'response' in result:
                translated = result['response'].strip()
                # 清理可能的多余文本
                translated = re.sub(r'^(中文翻译：|翻译：|答：)', '', translated).strip()
                return translated
            else:
                print(f"本地模型响应格式错误: {result}")
                return text
                
        except Exception as e:
            print(f"本地模型翻译错误: {e}")
            return text
    
    def is_available(self) -> bool:
        """检查本地模型是否可用"""
        try:
            response = self.session.get(f"{self.base_url}/api/tags", timeout=5)
            return response.status_code == 200
        except:
            return False

def extract_sentences(text: str) -> List[str]:
    """提取句子"""
    lines = text.split('\n')
    clean_lines = []
    
    for line in lines:
        line = line.strip()
        if line and not line.startswith(('Human:', 'Assistant:', '```', '#', '//', '/*')):
            line = re.sub(r'//.*$', '', line)
            line = re.sub(r'/\*.*?\*/', '', line)
            line = line.strip()
            if line:
                clean_lines.append(line)
    
    content = ' '.join(clean_lines)
    sentences = re.split(r'[.!?]+\s+', content)
    
    result = []
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 15:  # 只保留较长的句子
            if not sentence[-1] in '.!?':
                sentence += '。'
            result.append(sentence)
    
    return result

def process_dataset(input_file: str, output_file: str, translator: LLMTranslator, 
                   max_entries: int = None) -> Dict[str, int]:
    """处理数据集"""
    stats = {
        'total_entries': 0,
        'processed_entries': 0,
        'generated_qa_pairs': 0,
        'errors': 0
    }
    
    if not translator.is_available():
        print("警告: 翻译器不可用，请检查配置")
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                if max_entries and stats['total_entries'] >= max_entries:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                try:
                    entry = json.loads(line)
                    stats['total_entries'] += 1
                    
                    print(f"处理第 {stats['total_entries']} 个条目...")
                    
                    original_text = entry.get('text', '')
                    sentences = extract_sentences(original_text)
                    
                    if not sentences:
                        continue
                    
                    translated_count = 0
                    for i, sentence in enumerate(sentences):
                        try:
                            print(f"  翻译句子 {i+1}/{len(sentences)}: {sentence[:50]}...")
                            
                            translated = translator.translate(sentence)
                            
                            if translated and translated != sentence:
                                qa_entry = {
                                    "text": f"Human:\n请将以下英文翻译成中文：\n{sentence}\n\nAssistant:\n{translated}",
                                    "metadata": {
                                        "original_entry_id": stats['total_entries'],
                                        "sentence_id": i + 1,
                                        "original_text": sentence,
                                        "translated_text": translated,
                                        "translator_type": translator.__class__.__name__,
                                        "source": "llm_translation"
                                    }
                                }
                                
                                outfile.write(json.dumps(qa_entry, ensure_ascii=False) + '\n')
                                stats['generated_qa_pairs'] += 1
                                translated_count += 1
                            
                            # 添加延迟避免API限制
                            time.sleep(0.5)
                                
                        except Exception as e:
                            print(f"    翻译句子时出错: {e}")
                            continue
                    
                    if translated_count > 0:
                        stats['processed_entries'] += 1
                        print(f"  生成了 {translated_count} 个问答对")
                    else:
                        print(f"  未生成问答对")
                    
                    print()
                
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    stats['errors'] += 1
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    stats['errors'] += 1
                    continue
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return stats
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return stats
    
    return stats

def main():
    parser = argparse.ArgumentParser(description='大模型翻译脚本')
    parser.add_argument('--input', '-i', default='english_data.jsonl', help='输入文件')
    parser.add_argument('--output', '-o', default='llm_translated_dataset.jsonl', help='输出文件')
    parser.add_argument('--max-entries', '-n', type=int, default=5, help='最大处理条目数')
    
    # 模型选择
    parser.add_argument('--model-type', choices=['openai', 'claude', 'local'], 
                       default='local', help='模型类型')
    
    # OpenAI参数
    parser.add_argument('--openai-key', help='OpenAI API密钥')
    parser.add_argument('--openai-model', default='gpt-3.5-turbo', help='OpenAI模型名称')
    parser.add_argument('--openai-base-url', help='OpenAI API基础URL')
    
    # Claude参数
    parser.add_argument('--claude-key', help='Claude API密钥')
    parser.add_argument('--claude-model', default='claude-3-haiku-20240307', help='Claude模型名称')
    
    # 本地模型参数
    parser.add_argument('--local-url', default='http://localhost:11434', help='本地模型API地址')
    parser.add_argument('--local-model', default='qwen2:7b', help='本地模型名称')
    
    args = parser.parse_args()
    
    print(f"大模型翻译脚本")
    print(f"输入文件: {args.input}")
    print(f"输出文件: {args.output}")
    print(f"模型类型: {args.model_type}")
    print(f"最大处理条目数: {args.max_entries}")
    print()
    
    # 创建翻译器
    translator = None
    if args.model_type == 'openai':
        api_key = args.openai_key or os.getenv('OPENAI_API_KEY')
        if not api_key:
            print("错误: 需要提供OpenAI API密钥")
            return
        translator = OpenAITranslator(api_key, args.openai_model, args.openai_base_url)
        print(f"使用OpenAI模型: {args.openai_model}")
        
    elif args.model_type == 'claude':
        api_key = args.claude_key or os.getenv('CLAUDE_API_KEY')
        if not api_key:
            print("错误: 需要提供Claude API密钥")
            return
        translator = ClaudeTranslator(api_key, args.claude_model)
        print(f"使用Claude模型: {args.claude_model}")
        
    elif args.model_type == 'local':
        translator = LocalLLMTranslator(args.local_url, args.local_model)
        print(f"使用本地模型: {args.local_model} @ {args.local_url}")
    
    if not translator:
        print("错误: 无法创建翻译器")
        return
    
    # 检查模型可用性
    if not translator.is_available():
        print("警告: 模型可能不可用，但将继续尝试...")
    
    # 处理数据集
    start_time = time.time()
    stats = process_dataset(args.input, args.output, translator, args.max_entries)
    end_time = time.time()
    
    # 显示结果
    print("翻译完成!")
    print(f"总耗时: {end_time - start_time:.2f} 秒")
    print(f"总条目数: {stats['total_entries']}")
    print(f"成功处理条目数: {stats['processed_entries']}")
    print(f"生成问答对数: {stats['generated_qa_pairs']}")
    print(f"错误数: {stats['errors']}")
    
    if stats['total_entries'] > 0:
        success_rate = stats['processed_entries'] / stats['total_entries'] * 100
        print(f"成功率: {success_rate:.2f}%")
    
    print(f"\n翻译结果已保存到: {args.output}")

if __name__ == "__main__":
    main()
