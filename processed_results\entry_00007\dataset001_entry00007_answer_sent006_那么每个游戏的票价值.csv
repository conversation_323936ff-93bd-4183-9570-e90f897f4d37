﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
那么每个游戏的票价值为$15 / 5个游戏 = $3,"['那么', '每', '个', '游戏', '的', '票价值', '为', '$', '15', '/', '5', '个', '游戏', '=', '$', '3']","['那么', '每个', '游戏', '的', '票价', '值', '为', '$', '15', '/', '5', '个', '游戏', '=', '$', '3']","['AD', 'DT', 'M', 'NN', 'DEG', 'NN', 'VC', 'PU', 'CD', 'PU', 'CD', 'M', 'NN', 'PU', 'PU', 'CD']","['r', 'r', 'q', 'n', 'u', 'n', 'vl', 'w', 'm', 'w', 'm', 'q', 'n', 'w', 'w', 'm']","['c', 'r', 'q', 'n', 'u', 'n', 'v', 'w', 'm', 'w', 'm', 'q', 'n', 'w', 'w', 'm']","[('15', 'INTEGER', 8, 9), ('3', 'INTEGER', 15, 16)]",[],"[('15', 'CARDINAL', 8, 9), ('5', 'CARDINAL', 10, 11), ('3', 'CARDINAL', 15, 16)]","[[('那么', 'ARGM-DIS', 0, 1), ('每个游戏的票价值', 'ARG0', 1, 6), ('为', 'PRED', 6, 7), ('15/5个游戏', 'ARG1', 8, 13)]]","[(7, 'advmod'), (4, 'det'), (2, 'clf'), (6, 'assmod'), (4, 'assm'), (7, 'top'), (0, 'root'), (9, 'punct'), (11, 'nummod'), (11, 'punct'), (13, 'nummod'), (13, 'clf'), (16, 'dep'), (16, 'punct'), (16, 'punct'), (7, 'attr')]","[[(7, 'mConj')], [(3, 'Sco')], [(4, 'Qp')], [(6, 'Host')], [(4, 'mAux')], [(7, 'Exp')], [(0, 'Root')], [(7, 'mPunc')], [(7, 'Clas')], [(9, 'mPunc')], [(12, 'Quan')], [(13, 'Qp')], [(7, 'Clas')], [(13, 'mPunc')], [(13, 'mPunc')], [(13, 'Quan')]]","(TOP
  (IP
    (ADVP (AD 那么))
    (NP
      (DNP (NP (DP (DT 每) (CLP (M 个))) (NP (NN 游戏))) (DEG 的))
      (NP (NN 票价值)))
    (VP
      (VP
        (VC 为)
        (NP
          (QP (PU $) (CD 15) (PU /) (CD 5) (CLP (M 个)))
          (NP (NN 游戏))))
      (PU =)
      (PU $)
      (QP (CD 3)))))"
