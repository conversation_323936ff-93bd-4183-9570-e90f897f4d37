﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
往返机票费用预估为$1000,"['往返', '机票', '费用', '预估', '为', '$', '1000']","['往返', '机票', '费用', '预估', '为', '$', '1000']","['NN', 'NN', 'NN', 'VV', 'VC', 'PU', 'CD']","['v', 'n', 'n', 'v', 'vl', 'w', 'm']","['vn', 'n', 'n', 'v', 'v', 'w', 'm']","[('1000', 'MONEY', 6, 7)]",[],"[('1000', 'MONEY', 6, 7)]","[[('往返机票费用', 'ARG1', 0, 3), ('预估', 'PRED', 3, 4), ('$1000', 'ARG1-PRD', 5, 7)], [('往返机票费用', 'ARG0', 0, 3), ('为', 'PRED', 4, 5), ('$1000', 'ARG1', 5, 7)]]","[(3, 'nn'), (3, 'nn'), (4, 'nsubj'), (0, 'root'), (4, 'dep'), (7, 'punct'), (4, 'dep')]","[[(2, 'Desc')], [(3, 'Host')], [(4, 'Exp'), (5, 'Exp')], [(7, 'mRang')], [(4, 'dCont')], [(0, 'Root'), (5, 'mPunc')], [(5, 'Clas')]]","(TOP
  (IP
    (NP (NP (NN 往返) (NN 机票)) (NP (NN 费用)))
    (VP (VCP (VV 预估) (VC 为)) (QP (NP (PU $)) (QP (CD 1000))))))"
