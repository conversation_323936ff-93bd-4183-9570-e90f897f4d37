﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
并可以要求商家承担相应的赔偿责任,"['并', '可以', '要求', '商家', '承担', '相应', '的', '赔偿', '责任']","['并', '可以', '要求', '商家', '承担', '相应', '的', '赔偿', '责任']","['AD', 'VV', 'VV', 'NN', 'VV', 'JJ', 'DEG', 'NN', 'NN']","['c', 'vu', 'v', 'n', 'v', 'a', 'u', 'v', 'n']","['c', 'v', 'v', 'n', 'v', 'v', 'u', 'vn', 'n']",[],[],[],"[[('并', 'ARGM-DIS', 0, 1), ('要求', 'PRED', 2, 3), ('商家', 'ARG1', 3, 4), ('承担相应的赔偿责任', 'ARG2', 4, 9)], [('承担', 'PRED', 4, 5), ('相应的赔偿责任', 'ARG1', 5, 9)]]","[(3, 'advmod'), (3, 'mmod'), (0, 'root'), (3, 'dobj'), (3, 'dep'), (9, 'assmod'), (6, 'assm'), (9, 'nn'), (5, 'dobj')]","[[(3, 'mConj')], [(3, 'mMod')], [(0, 'Root')], [(3, 'Datv'), (5, 'Agt')], [(3, 'dCont')], [(9, 'Desc')], [(6, 'mAux')], [(9, 'Desc')], [(5, 'Cont')]]","(TOP
  (IP
    (VP
      (ADVP (AD 并))
      (VP
        (VV 可以)
        (VP
          (VV 要求)
          (NP (NN 商家))
          (IP
            (VP
              (VV 承担)
              (NP (DNP (ADJP (JJ 相应)) (DEG 的)) (NP (NN 赔偿) (NN 责任))))))))))"
