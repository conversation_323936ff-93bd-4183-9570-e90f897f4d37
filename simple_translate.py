#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版英文数据集翻译脚本
使用免费的Google翻译API将英文数据集翻译成中文问答对
"""

import json
import re
import time
import requests
from typing import Dict, Any, List

def translate_text(text: str, source_lang: str = 'en', target_lang: str = 'zh-cn') -> str:
    """使用Google翻译API翻译文本"""
    if not text.strip():
        return text
    
    try:
        # 使用免费的Google翻译API
        url = "https://translate.googleapis.com/translate_a/single"
        params = {
            'client': 'gtx',
            'sl': source_lang,
            'tl': target_lang,
            'dt': 't',
            'q': text
        }
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        response = requests.get(url, params=params, headers=headers, timeout=10)
        response.raise_for_status()
        
        result = response.json()
        if result and result[0]:
            translated_text = ''.join([item[0] for item in result[0] if item[0]])
            return translated_text.strip()
            
    except Exception as e:
        print(f"翻译错误: {e}")
        return text
    
    return text

def split_into_sentences(text: str) -> List[str]:
    """将文本分割成句子"""
    # 简单的句子分割
    sentences = re.split(r'[.!?]+\s*', text)
    result = []
    
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 10:  # 只保留长度大于10的句子
            # 确保句子以标点结尾
            if not sentence[-1] in '.!?':
                sentence += '.'
            result.append(sentence)
    
    return result

def extract_meaningful_content(text: str) -> str:
    """提取有意义的内容"""
    lines = text.split('\n')
    content_lines = []
    
    for line in lines:
        line = line.strip()
        # 跳过标记行和空行
        if line and not line.startswith(('Human:', 'Assistant:', '```', '#')):
            content_lines.append(line)
    
    return ' '.join(content_lines)

def translate_entry(entry: Dict[str, Any], entry_id: int) -> List[Dict[str, Any]]:
    """翻译单个条目，生成多个问答对"""
    original_text = entry.get('text', '')
    if not original_text.strip():
        return []
    
    # 提取有意义的内容
    content = extract_meaningful_content(original_text)
    if not content.strip():
        return []
    
    # 分割成句子
    sentences = split_into_sentences(content)
    if not sentences:
        return []
    
    translated_entries = []
    
    for i, sentence in enumerate(sentences):
        try:
            print(f"  翻译句子 {i+1}/{len(sentences)}: {sentence[:50]}...")
            
            # 翻译句子
            translated_sentence = translate_text(sentence)
            
            if translated_sentence and translated_sentence != sentence:
                # 创建问答格式
                qa_entry = {
                    "text": f"Human:\n请将以下英文翻译成中文：\n{sentence}\n\nAssistant:\n{translated_sentence}",
                    "metadata": {
                        "original_entry_id": entry_id,
                        "sentence_id": i + 1,
                        "original_text": sentence,
                        "translated_text": translated_sentence,
                        "source": "english_dataset_translation"
                    }
                }
                
                translated_entries.append(qa_entry)
            
            # 添加延迟避免API限制
            time.sleep(0.5)
            
        except Exception as e:
            print(f"    翻译句子时出错: {e}")
            continue
    
    return translated_entries

def main():
    """主函数"""
    input_file = 'english_data.jsonl'
    output_file = 'translated_qa_dataset.jsonl'
    max_entries = 50  # 限制处理条目数，避免API限制
    
    print(f"开始翻译英文数据集...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"最大处理条目数: {max_entries}")
    print()
    
    total_entries = 0
    processed_entries = 0
    generated_qa_pairs = 0
    errors = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                if total_entries >= max_entries:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # 解析JSON
                    entry = json.loads(line)
                    total_entries += 1
                    
                    print(f"处理第 {total_entries} 个条目...")
                    
                    # 翻译条目
                    translated_entries = translate_entry(entry, total_entries)
                    
                    # 写入翻译结果
                    for translated_entry in translated_entries:
                        outfile.write(json.dumps(translated_entry, ensure_ascii=False) + '\n')
                        generated_qa_pairs += 1
                    
                    if translated_entries:
                        processed_entries += 1
                        print(f"  生成了 {len(translated_entries)} 个问答对")
                    else:
                        print(f"  未生成问答对")
                    
                    print()
                
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    errors += 1
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    errors += 1
                    continue
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return
    
    # 打印统计信息
    print("翻译完成!")
    print(f"总条目数: {total_entries}")
    print(f"成功处理条目数: {processed_entries}")
    print(f"生成问答对数: {generated_qa_pairs}")
    print(f"错误数: {errors}")
    
    if total_entries > 0:
        success_rate = processed_entries / total_entries * 100
        print(f"成功率: {success_rate:.2f}%")
    
    print(f"\n翻译结果已保存到: {output_file}")
    
    # 显示几个示例
    if generated_qa_pairs > 0:
        print("\n示例问答对:")
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if i >= 3:  # 只显示前3个
                        break
                    entry = json.loads(line)
                    print(f"\n示例 {i+1}:")
                    print(entry['text'])
                    print("-" * 50)
        except Exception as e:
            print(f"读取示例时出错: {e}")

if __name__ == "__main__":
    main()
