#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
大模型翻译配置文件
包含各种大模型的配置和测试功能
"""

import os
import requests
import json

# 模型配置
MODEL_CONFIGS = {
    "openai": {
        "gpt-4": {
            "model": "gpt-4",
            "max_tokens": 2000,
            "temperature": 0.3,
            "cost_per_1k_tokens": 0.03  # 美元
        },
        "gpt-3.5-turbo": {
            "model": "gpt-3.5-turbo",
            "max_tokens": 1000,
            "temperature": 0.3,
            "cost_per_1k_tokens": 0.002
        },
        "gpt-4-turbo": {
            "model": "gpt-4-turbo-preview",
            "max_tokens": 2000,
            "temperature": 0.3,
            "cost_per_1k_tokens": 0.01
        }
    },
    "claude": {
        "claude-3-opus": {
            "model": "claude-3-opus-20240229",
            "max_tokens": 2000,
            "temperature": 0.3,
            "cost_per_1k_tokens": 0.015
        },
        "claude-3-sonnet": {
            "model": "claude-3-sonnet-20240229",
            "max_tokens": 1500,
            "temperature": 0.3,
            "cost_per_1k_tokens": 0.003
        },
        "claude-3-haiku": {
            "model": "claude-3-haiku-20240307",
            "max_tokens": 1000,
            "temperature": 0.3,
            "cost_per_1k_tokens": 0.00025
        }
    },
    "local": {
        "qwen2-7b": {
            "model": "qwen2:7b",
            "url": "http://localhost:11434",
            "description": "通义千问2 7B模型"
        },
        "llama3-8b": {
            "model": "llama3:8b",
            "url": "http://localhost:11434",
            "description": "Llama 3 8B模型"
        },
        "gemma-7b": {
            "model": "gemma:7b",
            "url": "http://localhost:11434",
            "description": "Google Gemma 7B模型"
        },
        "chatglm3-6b": {
            "model": "chatglm3:6b",
            "url": "http://localhost:11434",
            "description": "ChatGLM3 6B模型"
        }
    }
}

# 翻译提示词模板
TRANSLATION_PROMPTS = {
    "standard": """你是一个专业的英中翻译专家。请将用户提供的英文文本翻译成自然流畅的中文。

翻译要求：
1. 保持原文的意思和语气
2. 使用自然流畅的中文表达
3. 对于编程术语，使用常见的中文翻译或保留英文
4. 保持原文的格式和结构
5. 只返回翻译结果，不要添加额外说明""",

    "technical": """你是专业的技术文档翻译专家。请将英文技术内容翻译成准确的中文。

要求：
1. 准确翻译技术术语
2. 保持代码和命令不变
3. 使用规范的技术中文表达
4. 保持原文逻辑结构
5. 只返回翻译结果""",

    "conversational": """你是专业的对话翻译专家。请将英文对话翻译成自然的中文对话。

要求：
1. 保持对话的语气和风格
2. 使用口语化的中文表达
3. 保持对话的自然流畅
4. 适当调整语序使其更符合中文习惯
5. 只返回翻译结果""",

    "academic": """你是学术文献翻译专家。请将英文学术内容翻译成严谨的中文。

要求：
1. 使用准确的学术术语
2. 保持严谨的表达风格
3. 准确传达学术概念
4. 保持逻辑清晰
5. 只返回翻译结果"""
}

def test_openai_connection(api_key: str, base_url: str = None) -> bool:
    """测试OpenAI API连接"""
    try:
        url = f"{base_url or 'https://api.openai.com/v1'}/models"
        headers = {"Authorization": f"Bearer {api_key}"}
        response = requests.get(url, headers=headers, timeout=10)
        return response.status_code == 200
    except Exception as e:
        print(f"OpenAI连接测试失败: {e}")
        return False

def test_claude_connection(api_key: str) -> bool:
    """测试Claude API连接"""
    try:
        # Claude没有简单的测试端点，这里简化处理
        return bool(api_key and len(api_key) > 10)
    except Exception as e:
        print(f"Claude连接测试失败: {e}")
        return False

def test_local_model_connection(base_url: str = "http://localhost:11434") -> bool:
    """测试本地模型连接"""
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=5)
        return response.status_code == 200
    except Exception as e:
        print(f"本地模型连接测试失败: {e}")
        return False

def list_available_models(base_url: str = "http://localhost:11434") -> list:
    """列出可用的本地模型"""
    try:
        response = requests.get(f"{base_url}/api/tags", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return [model['name'] for model in data.get('models', [])]
    except Exception as e:
        print(f"获取模型列表失败: {e}")
    return []

def estimate_cost(text_length: int, model_type: str, model_name: str) -> float:
    """估算翻译成本（仅适用于付费API）"""
    if model_type not in MODEL_CONFIGS:
        return 0.0
    
    if model_name not in MODEL_CONFIGS[model_type]:
        return 0.0
    
    config = MODEL_CONFIGS[model_type][model_name]
    if 'cost_per_1k_tokens' not in config:
        return 0.0
    
    # 粗略估算：英文文本长度 / 4 ≈ token数量
    estimated_tokens = text_length / 4
    cost = (estimated_tokens / 1000) * config['cost_per_1k_tokens']
    return cost

def get_recommended_model(text_length: int, quality_priority: str = "balanced") -> dict:
    """根据文本长度和质量要求推荐模型"""
    recommendations = {
        "high_quality": {
            "short": ("openai", "gpt-4"),
            "medium": ("claude", "claude-3-sonnet"),
            "long": ("claude", "claude-3-haiku")
        },
        "balanced": {
            "short": ("openai", "gpt-3.5-turbo"),
            "medium": ("claude", "claude-3-haiku"),
            "long": ("local", "qwen2-7b")
        },
        "cost_effective": {
            "short": ("local", "qwen2-7b"),
            "medium": ("local", "llama3-8b"),
            "long": ("local", "qwen2-7b")
        }
    }
    
    if text_length < 1000:
        size = "short"
    elif text_length < 5000:
        size = "medium"
    else:
        size = "long"
    
    model_type, model_name = recommendations[quality_priority][size]
    return {
        "type": model_type,
        "name": model_name,
        "reason": f"推荐用于{size}文本的{quality_priority}方案"
    }

def main():
    """配置测试和模型推荐"""
    print("=== 大模型翻译配置工具 ===\n")
    
    # 测试连接
    print("1. 测试模型连接:")
    
    # 测试OpenAI
    openai_key = os.getenv('OPENAI_API_KEY')
    if openai_key:
        if test_openai_connection(openai_key):
            print("✅ OpenAI API 连接正常")
        else:
            print("❌ OpenAI API 连接失败")
    else:
        print("⚠️  未设置 OPENAI_API_KEY 环境变量")
    
    # 测试Claude
    claude_key = os.getenv('CLAUDE_API_KEY')
    if claude_key:
        if test_claude_connection(claude_key):
            print("✅ Claude API 密钥已设置")
        else:
            print("❌ Claude API 密钥无效")
    else:
        print("⚠️  未设置 CLAUDE_API_KEY 环境变量")
    
    # 测试本地模型
    if test_local_model_connection():
        print("✅ 本地模型服务连接正常")
        models = list_available_models()
        if models:
            print(f"   可用模型: {', '.join(models[:3])}{'...' if len(models) > 3 else ''}")
    else:
        print("❌ 本地模型服务连接失败")
    
    print("\n2. 模型推荐:")
    
    # 示例推荐
    test_lengths = [500, 2000, 10000]
    for length in test_lengths:
        for priority in ["high_quality", "balanced", "cost_effective"]:
            rec = get_recommended_model(length, priority)
            cost = estimate_cost(length, rec["type"], rec["name"])
            cost_str = f" (约${cost:.4f})" if cost > 0 else " (免费)"
            print(f"   {length}字符, {priority}: {rec['type']}/{rec['name']}{cost_str}")
    
    print("\n3. 使用示例:")
    print("# 使用本地模型 (推荐)")
    print("python llm_translate.py --model-type local --local-model qwen2:7b --max-entries 5")
    print()
    print("# 使用OpenAI")
    print("export OPENAI_API_KEY='your-key-here'")
    print("python llm_translate.py --model-type openai --openai-model gpt-3.5-turbo --max-entries 3")
    print()
    print("# 使用Claude")
    print("export CLAUDE_API_KEY='your-key-here'")
    print("python llm_translate.py --model-type claude --claude-model claude-3-haiku-20240307 --max-entries 3")

if __name__ == "__main__":
    main()
