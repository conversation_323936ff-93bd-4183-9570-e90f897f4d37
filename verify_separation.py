#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证中英文数据分离结果的脚本
"""

import json
import re
from typing import Dict, Any

def contains_chinese(text: str) -> bool:
    """检查文本是否包含中文字符"""
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    return bool(chinese_pattern.search(text))

def get_chinese_char_ratio(text: str) -> float:
    """计算文本中中文字符的比例"""
    if not text:
        return 0.0
    
    text_no_space = re.sub(r'\s', '', text)
    if not text_no_space:
        return 0.0
    
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text_no_space)
    return len(chinese_chars) / len(text_no_space)

def count_english_words(text: str) -> int:
    """计算文本中英文单词的数量"""
    english_words = re.findall(r'\b[a-zA-Z]+\b', text)
    return len(english_words)

def count_chinese_words(text: str) -> int:
    """计算文本中中文字符的数量"""
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
    return len(chinese_chars)

def analyze_file(filename: str, expected_language: str):
    """分析文件中的语言分布"""
    print(f"\n分析文件: {filename}")
    print(f"预期语言: {expected_language}")
    print("-" * 50)

    total_entries = 0
    chinese_entries = 0
    english_entries = 0
    mixed_entries = 0

    try:
        with open(filename, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    entry = json.loads(line)
                    text = entry.get('text', '')
                    total_entries += 1

                    chinese_count = count_chinese_words(text)
                    english_count = count_english_words(text)
                    has_chinese = contains_chinese(text)

                    # 使用与分离脚本相同的分类逻辑
                    has_significant_chinese = chinese_count >= 3
                    has_significant_english = english_count >= 3

                    if has_significant_chinese and has_significant_english:
                        mixed_entries += 1
                    elif has_significant_chinese or (has_chinese and chinese_count > 0):
                        chinese_entries += 1
                    else:
                        english_entries += 1

                    # 显示前几个条目的详细信息
                    if line_num <= 3:
                        print(f"条目 {line_num}:")
                        print(f"  中文词数: {chinese_count}")
                        print(f"  英文词数: {english_count}")
                        print(f"  包含中文: {has_chinese}")
                        print(f"  文本预览: {text[:100]}...")
                        print()

                except json.JSONDecodeError:
                    print(f"第 {line_num} 行JSON解析错误")
                    continue

    except FileNotFoundError:
        print(f"文件 {filename} 不存在")
        return

    print(f"总条目数: {total_entries}")
    print(f"中文条目: {chinese_entries} ({chinese_entries/total_entries*100:.2f}%)")
    print(f"英文条目: {english_entries} ({english_entries/total_entries*100:.2f}%)")
    print(f"混合条目: {mixed_entries} ({mixed_entries/total_entries*100:.2f}%)")

    # 验证分类是否正确
    if expected_language == "chinese":
        correct_ratio = chinese_entries / total_entries * 100
        print(f"分类正确率: {correct_ratio:.2f}%")
    elif expected_language == "english":
        correct_ratio = english_entries / total_entries * 100
        print(f"分类正确率: {correct_ratio:.2f}%")
    else:  # mixed
        correct_ratio = mixed_entries / total_entries * 100
        print(f"分类正确率: {correct_ratio:.2f}%")

def main():
    """主函数"""
    print("验证中英文数据分离结果")
    print("=" * 60)

    # 分析中文文件
    analyze_file("chinese_data.jsonl", "chinese")

    # 分析英文文件
    analyze_file("english_data.jsonl", "english")

    # 分析混合文件
    analyze_file("mixed_data.jsonl", "mixed")

    print("\n" + "=" * 60)
    print("验证完成")

if __name__ == "__main__":
    main()
