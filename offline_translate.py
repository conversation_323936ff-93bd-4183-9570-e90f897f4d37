#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线版英文数据集翻译脚本
使用本地词典和规则进行基础翻译，生成问答对格式
适用于网络受限或需要快速测试的场景
"""

import json
import re
from typing import Dict, Any, List

# 简单的英中词典（可以扩展）
TRANSLATION_DICT = {
    # 常用动词
    'create': '创建', 'write': '写', 'make': '制作', 'build': '构建', 'develop': '开发',
    'update': '更新', 'modify': '修改', 'change': '改变', 'add': '添加', 'remove': '移除',
    'delete': '删除', 'insert': '插入', 'find': '查找', 'search': '搜索', 'get': '获取',
    'set': '设置', 'put': '放置', 'take': '取得', 'give': '给予', 'show': '显示',
    'display': '显示', 'print': '打印', 'output': '输出', 'input': '输入', 'read': '读取',
    'load': '加载', 'save': '保存', 'store': '存储', 'process': '处理', 'handle': '处理',
    'manage': '管理', 'control': '控制', 'run': '运行', 'execute': '执行', 'perform': '执行',
    'calculate': '计算', 'compute': '计算', 'analyze': '分析', 'check': '检查', 'test': '测试',
    'validate': '验证', 'verify': '验证', 'confirm': '确认', 'ensure': '确保', 'implement': '实现',
    
    # 常用名词
    'script': '脚本', 'code': '代码', 'program': '程序', 'function': '函数', 'method': '方法',
    'class': '类', 'object': '对象', 'variable': '变量', 'parameter': '参数', 'argument': '参数',
    'value': '值', 'data': '数据', 'information': '信息', 'result': '结果', 'output': '输出',
    'input': '输入', 'file': '文件', 'document': '文档', 'text': '文本', 'string': '字符串',
    'number': '数字', 'integer': '整数', 'float': '浮点数', 'boolean': '布尔值', 'array': '数组',
    'list': '列表', 'dictionary': '字典', 'database': '数据库', 'table': '表', 'record': '记录',
    'field': '字段', 'column': '列', 'row': '行', 'index': '索引', 'key': '键',
    'system': '系统', 'application': '应用程序', 'software': '软件', 'tool': '工具', 'library': '库',
    'framework': '框架', 'module': '模块', 'package': '包', 'component': '组件', 'service': '服务',
    'api': 'API', 'interface': '接口', 'protocol': '协议', 'format': '格式', 'structure': '结构',
    'algorithm': '算法', 'logic': '逻辑', 'condition': '条件', 'statement': '语句', 'expression': '表达式',
    'loop': '循环', 'iteration': '迭代', 'recursion': '递归', 'exception': '异常', 'error': '错误',
    'bug': '错误', 'issue': '问题', 'problem': '问题', 'solution': '解决方案', 'answer': '答案',
    'question': '问题', 'example': '示例', 'sample': '样本', 'template': '模板', 'pattern': '模式',
    'model': '模型', 'design': '设计', 'architecture': '架构', 'structure': '结构', 'layout': '布局',
    'configuration': '配置', 'setting': '设置', 'option': '选项', 'choice': '选择', 'selection': '选择',
    'user': '用户', 'client': '客户端', 'server': '服务器', 'network': '网络', 'connection': '连接',
    'request': '请求', 'response': '响应', 'message': '消息', 'signal': '信号', 'event': '事件',
    'action': '动作', 'operation': '操作', 'task': '任务', 'job': '作业', 'work': '工作',
    'project': '项目', 'version': '版本', 'release': '发布', 'update': '更新', 'upgrade': '升级',
    
    # 常用形容词
    'new': '新的', 'old': '旧的', 'good': '好的', 'bad': '坏的', 'best': '最好的',
    'better': '更好的', 'worse': '更坏的', 'big': '大的', 'small': '小的', 'large': '大的',
    'little': '小的', 'long': '长的', 'short': '短的', 'high': '高的', 'low': '低的',
    'fast': '快的', 'slow': '慢的', 'quick': '快速的', 'easy': '容易的', 'hard': '困难的',
    'simple': '简单的', 'complex': '复杂的', 'basic': '基本的', 'advanced': '高级的', 'professional': '专业的',
    'important': '重要的', 'useful': '有用的', 'necessary': '必要的', 'required': '必需的', 'optional': '可选的',
    'available': '可用的', 'ready': '准备好的', 'complete': '完整的', 'finished': '完成的', 'done': '完成的',
    'correct': '正确的', 'wrong': '错误的', 'true': '真的', 'false': '假的', 'valid': '有效的',
    'invalid': '无效的', 'empty': '空的', 'full': '满的', 'open': '打开的', 'closed': '关闭的',
    
    # 编程相关
    'python': 'Python', 'java': 'Java', 'javascript': 'JavaScript', 'html': 'HTML', 'css': 'CSS',
    'sql': 'SQL', 'json': 'JSON', 'xml': 'XML', 'csv': 'CSV', 'api': 'API',
    'http': 'HTTP', 'https': 'HTTPS', 'url': 'URL', 'uri': 'URI', 'web': '网页',
    'website': '网站', 'page': '页面', 'browser': '浏览器', 'server': '服务器', 'client': '客户端',
    'database': '数据库', 'mysql': 'MySQL', 'postgresql': 'PostgreSQL', 'mongodb': 'MongoDB', 'redis': 'Redis',
    'git': 'Git', 'github': 'GitHub', 'repository': '仓库', 'commit': '提交', 'branch': '分支',
    'merge': '合并', 'pull': '拉取', 'push': '推送', 'clone': '克隆', 'fork': '分叉',
    
    # 其他常用词
    'and': '和', 'or': '或', 'not': '不', 'but': '但是', 'if': '如果',
    'then': '那么', 'else': '否则', 'when': '当', 'where': '哪里', 'what': '什么',
    'how': '如何', 'why': '为什么', 'who': '谁', 'which': '哪个', 'that': '那个',
    'this': '这个', 'these': '这些', 'those': '那些', 'all': '所有', 'some': '一些',
    'many': '许多', 'few': '少数', 'more': '更多', 'less': '更少', 'most': '最多',
    'first': '第一', 'last': '最后', 'next': '下一个', 'previous': '上一个', 'current': '当前',
    'following': '以下', 'above': '上面', 'below': '下面', 'before': '之前', 'after': '之后',
    'during': '期间', 'between': '之间', 'among': '在...中', 'within': '在...内', 'without': '没有',
    'with': '与', 'from': '从', 'to': '到', 'for': '为了', 'by': '通过',
    'in': '在', 'on': '在...上', 'at': '在', 'of': '的', 'about': '关于',
    'over': '超过', 'under': '在...下', 'up': '向上', 'down': '向下', 'out': '出去',
    'into': '进入', 'onto': '到...上', 'off': '离开', 'through': '通过', 'across': '穿过',
}

def simple_translate(text: str) -> str:
    """简单的英译中函数"""
    if not text.strip():
        return text
    
    # 转换为小写进行匹配
    words = re.findall(r'\b\w+\b', text.lower())
    translated_words = []
    
    for word in words:
        if word in TRANSLATION_DICT:
            translated_words.append(TRANSLATION_DICT[word])
        else:
            # 保留原词
            translated_words.append(word)
    
    # 简单的句子重构
    if translated_words:
        result = ' '.join(translated_words)
        # 移除多余空格
        result = re.sub(r'\s+', ' ', result).strip()
        return result
    
    return text

def advanced_translate(text: str) -> str:
    """更高级的翻译函数，包含一些语法处理"""
    if not text.strip():
        return text
    
    # 预处理：处理常见短语
    phrases = {
        'how to': '如何',
        'what is': '什么是',
        'what are': '什么是',
        'how do': '如何',
        'how can': '如何能够',
        'in order to': '为了',
        'such as': '例如',
        'as well as': '以及',
        'more than': '超过',
        'less than': '少于',
        'at least': '至少',
        'at most': '最多',
        'for example': '例如',
        'that is': '即',
        'in other words': '换句话说',
        'on the other hand': '另一方面',
        'as a result': '结果',
        'in addition': '此外',
        'in conclusion': '总之',
    }
    
    text_lower = text.lower()
    for phrase, translation in phrases.items():
        text_lower = text_lower.replace(phrase, translation)
    
    # 分词并翻译
    words = re.findall(r'\b\w+\b', text_lower)
    translated_words = []
    
    for word in words:
        if word in TRANSLATION_DICT:
            translated_words.append(TRANSLATION_DICT[word])
        else:
            # 对于未知词汇，尝试一些简单的处理
            if word.endswith('ing'):
                base_word = word[:-3]
                if base_word in TRANSLATION_DICT:
                    translated_words.append(TRANSLATION_DICT[base_word] + '中')
                else:
                    translated_words.append(word)
            elif word.endswith('ed'):
                base_word = word[:-2]
                if base_word in TRANSLATION_DICT:
                    translated_words.append(TRANSLATION_DICT[base_word] + '了')
                else:
                    translated_words.append(word)
            elif word.endswith('s') and len(word) > 2:
                base_word = word[:-1]
                if base_word in TRANSLATION_DICT:
                    translated_words.append(TRANSLATION_DICT[base_word] + '们')
                else:
                    translated_words.append(word)
            else:
                translated_words.append(word)
    
    # 重构句子
    if translated_words:
        result = ' '.join(translated_words)
        # 清理和格式化
        result = re.sub(r'\s+', '', result)  # 中文不需要空格
        # 保留一些标点符号的空格
        result = re.sub(r'([.!?])', r'\1 ', result)
        result = result.strip()
        return result
    
    return text

def split_into_sentences(text: str) -> List[str]:
    """将文本分割成句子"""
    # 简单的句子分割
    sentences = re.split(r'[.!?]+\s*', text)
    result = []
    
    for sentence in sentences:
        sentence = sentence.strip()
        if sentence and len(sentence) > 5:  # 只保留长度大于5的句子
            # 确保句子以标点结尾
            if not sentence[-1] in '.!?':
                sentence += '.'
            result.append(sentence)
    
    return result

def extract_meaningful_content(text: str) -> str:
    """提取有意义的内容"""
    lines = text.split('\n')
    content_lines = []
    
    for line in lines:
        line = line.strip()
        # 跳过标记行、代码块和空行
        if line and not line.startswith(('Human:', 'Assistant:', '```', '#', '//', '/*', '*')):
            # 移除代码注释
            line = re.sub(r'//.*$', '', line)  # 单行注释
            line = re.sub(r'/\*.*?\*/', '', line)  # 多行注释
            line = line.strip()
            if line:
                content_lines.append(line)
    
    return ' '.join(content_lines)

def translate_entry(entry: Dict[str, Any], entry_id: int) -> List[Dict[str, Any]]:
    """翻译单个条目，生成多个问答对"""
    original_text = entry.get('text', '')
    if not original_text.strip():
        return []
    
    # 提取有意义的内容
    content = extract_meaningful_content(original_text)
    if not content.strip():
        return []
    
    # 分割成句子
    sentences = split_into_sentences(content)
    if not sentences:
        return []
    
    translated_entries = []
    
    for i, sentence in enumerate(sentences):
        try:
            print(f"  翻译句子 {i+1}/{len(sentences)}: {sentence[:50]}...")
            
            # 翻译句子
            translated_sentence = advanced_translate(sentence)
            
            if translated_sentence and translated_sentence != sentence:
                # 创建问答格式
                qa_entry = {
                    "text": f"Human:\n请将以下英文翻译成中文：\n{sentence}\n\nAssistant:\n{translated_sentence}",
                    "metadata": {
                        "original_entry_id": entry_id,
                        "sentence_id": i + 1,
                        "original_text": sentence,
                        "translated_text": translated_sentence,
                        "source": "offline_translation",
                        "translation_method": "rule_based"
                    }
                }
                
                translated_entries.append(qa_entry)
            
        except Exception as e:
            print(f"    翻译句子时出错: {e}")
            continue
    
    return translated_entries

def main():
    """主函数"""
    input_file = 'english_data.jsonl'
    output_file = 'offline_translated_qa_dataset.jsonl'
    max_entries = 20  # 限制处理条目数进行测试
    
    print(f"开始离线翻译英文数据集...")
    print(f"输入文件: {input_file}")
    print(f"输出文件: {output_file}")
    print(f"最大处理条目数: {max_entries}")
    print(f"翻译方法: 基于规则的离线翻译")
    print()
    
    total_entries = 0
    processed_entries = 0
    generated_qa_pairs = 0
    errors = 0
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8') as outfile:
            
            for line_num, line in enumerate(infile, 1):
                if total_entries >= max_entries:
                    break
                
                line = line.strip()
                if not line:
                    continue
                
                try:
                    # 解析JSON
                    entry = json.loads(line)
                    total_entries += 1
                    
                    print(f"处理第 {total_entries} 个条目...")
                    
                    # 翻译条目
                    translated_entries = translate_entry(entry, total_entries)
                    
                    # 写入翻译结果
                    for translated_entry in translated_entries:
                        outfile.write(json.dumps(translated_entry, ensure_ascii=False) + '\n')
                        generated_qa_pairs += 1
                    
                    if translated_entries:
                        processed_entries += 1
                        print(f"  生成了 {len(translated_entries)} 个问答对")
                    else:
                        print(f"  未生成问答对")
                    
                    print()
                
                except json.JSONDecodeError as e:
                    print(f"第 {line_num} 行JSON解析错误: {e}")
                    errors += 1
                    continue
                except Exception as e:
                    print(f"第 {line_num} 行处理错误: {e}")
                    errors += 1
                    continue
    
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return
    except Exception as e:
        print(f"处理文件时发生错误: {e}")
        return
    
    # 打印统计信息
    print("翻译完成!")
    print(f"总条目数: {total_entries}")
    print(f"成功处理条目数: {processed_entries}")
    print(f"生成问答对数: {generated_qa_pairs}")
    print(f"错误数: {errors}")
    
    if total_entries > 0:
        success_rate = processed_entries / total_entries * 100
        print(f"成功率: {success_rate:.2f}%")
    
    print(f"\n翻译结果已保存到: {output_file}")
    
    # 显示几个示例
    if generated_qa_pairs > 0:
        print("\n示例问答对:")
        try:
            with open(output_file, 'r', encoding='utf-8') as f:
                for i, line in enumerate(f):
                    if i >= 3:  # 只显示前3个
                        break
                    entry = json.loads(line)
                    print(f"\n示例 {i+1}:")
                    print(entry['text'])
                    print("-" * 50)
        except Exception as e:
            print(f"读取示例时出错: {e}")

if __name__ == "__main__":
    main()
