﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
泰根在地上找到了5张票，每张票价值$3，所以这些票总价值为5 * $3 = $15,"['泰根', '在', '地上', '找到', '了', '5', '张', '票', '，', '每', '张', '票', '价值', '$', '3', '，', '所以', '这些', '票', '总', '价值', '为', '5', '*', '$', '3', '=', '$', '15']","['泰根', '在', '地上', '找到', '了', '5', '张', '票', '，', '每', '张', '票', '价值', '$', '3', '，', '所以', '这些', '票', '总', '价值', '为', '5', '*', '$', '3', '=', '$', '15']","['NR', 'P', 'NN', 'VV', 'AS', 'CD', 'M', 'NN', 'PU', 'DT', 'M', 'NN', 'VV', 'PU', 'CD', 'PU', 'AD', 'DT', 'NN', 'JJ', 'NN', 'VC', 'CD', 'PU', 'PU', 'CD', 'PU', 'PU', 'CD']","['nh', 'p', 'nl', 'v', 'u', 'm', 'q', 'n', 'w', 'r', 'q', 'n', 'n', 'w', 'm', 'w', 'c', 'r', 'n', 'd', 'n', 'vl', 'm', 'w', 'w', 'm', 'w', 'w', 'm']","['nr', 'p', 's', 'v', 'u', 'm', 'q', 'n', 'w', 'r', 'q', 'n', 'n', 'w', 'm', 'w', 'c', 'r', 'n', 'b', 'n', 'v', 'm', 'w', 'w', 'm', 'w', 'w', 'm']","[('泰根', 'PERSON', 0, 1), ('5', 'INTEGER', 5, 6), ('3', 'INTEGER', 14, 15), ('5', 'INTEGER', 22, 23), ('3', 'INTEGER', 25, 26), ('15', 'INTEGER', 28, 29)]","[('泰根', 'nr', 0, 1)]","[('泰根', 'PERSON', 0, 1), ('5', 'CARDINAL', 5, 6), ('3', 'CARDINAL', 14, 15), ('5', 'CARDINAL', 22, 23), ('3', 'CARDINAL', 25, 26), ('15', 'CARDINAL', 28, 29)]","[[('泰根', 'ARG0', 0, 1), ('在地上', 'ARGM-LOC', 1, 3), ('找到', 'PRED', 3, 4), ('5张票', 'ARG1', 5, 8)], [('每张票', 'ARG0', 9, 12), ('价值', 'PRED', 12, 13)], [('所以', 'ARGM-DIS', 16, 17), ('这些票总价值', 'ARG0', 17, 21), ('为', 'PRED', 21, 22), ('5*$3=$15', 'ARG1', 22, 29)]]","[(4, 'nsubj'), (4, 'prep'), (2, 'pobj'), (0, 'root'), (4, 'asp'), (7, 'nummod'), (8, 'clf'), (4, 'dobj'), (4, 'punct'), (12, 'det'), (10, 'clf'), (13, 'nsubj'), (4, 'conj'), (15, 'punct'), (13, 'range'), (4, 'punct'), (22, 'advmod'), (19, 'det'), (21, 'nn'), (21, 'amod'), (22, 'top'), (4, 'conj'), (26, 'nummod'), (26, 'punct'), (26, 'punct'), (29, 'nummod'), (29, 'punct'), (29, 'punct'), (22, 'attr')]","[[(4, 'Agt')], [(3, 'mPrep')], [(4, 'Loc')], [(0, 'Root')], [(4, 'mTime')], [(7, 'Quan')], [(8, 'Qp')], [(4, 'Cont')], [(4, 'mPunc')], [(11, 'Sco')], [(12, 'Qp')], [(13, 'Exp')], [(4, 'eSucc')], [(13, 'mPunc')], [(13, 'Quan')], [(13, 'mPunc')], [(22, 'mConj')], [(19, 'Sco')], [(21, 'Host')], [(21, 'Desc')], [(22, 'Exp')], [(13, 'eSucc')], [(22, 'Clas')], [(23, 'mPunc')], [(23, 'mPunc')], [(22, 'Clas')], [(26, 'mPunc')], [(23, 'mPunc')], [(26, 'eCoo')]]","(TOP
  (IP
    (IP
      (NP (NR 泰根))
      (VP
        (PP (P 在) (NP (NN 地上)))
        (VP (VV 找到) (AS 了) (NP (QP (CD 5) (CLP (M 张))) (NP (NN 票))))))
    (PU ，)
    (IP
      (NP (DP (DT 每) (CLP (M 张))) (NP (NN 票)))
      (VP (VV 价值) (PU $) (QP (CD 3))))
    (PU ，)
    (IP
      (ADVP (AD 所以))
      (NP (NP (DP (DT 这些)) (NP (NN 票))) (ADJP (JJ 总)) (NP (NN 价值)))
      (VP
        (VC 为)
        (QP
          (QP (CD 5))
          (PU *)
          (PU $)
          (CD 3)
          (PU =)
          (PU $)
          (QP (CD 15)))))))"
