# 英文数据集翻译脚本 - 快速开始

## 🚀 一分钟快速开始

### 1. 检查文件
确保你有 `english_data.jsonl` 文件在当前目录下。

### 2. 选择脚本运行
根据你的需求选择以下任一命令：

#### 推荐：增强版脚本（离线模式）
```bash
python enhanced_translate.py --mode offline --max-entries 10
```
- ✅ 无需网络连接
- ✅ 处理速度快
- ✅ 支持多种翻译模式
- ✅ 翻译质量较好

#### 简单：离线翻译脚本
```bash
python offline_translate.py
```
- ✅ 无需网络连接
- ✅ 代码简单易懂
- ⚠️ 翻译质量一般

#### 高质量：在线翻译（需要网络）
```bash
python enhanced_translate.py --mode online --max-entries 5
```
- ⚠️ 需要网络连接
- ✅ 翻译质量高
- ⚠️ 可能有API限制

## 📁 输出文件说明

运行脚本后，会生成以下文件：

| 脚本 | 输出文件 | 格式 |
|------|---------|------|
| enhanced_translate.py | enhanced_translated_dataset.jsonl | 问答对格式 |
| offline_translate.py | offline_translated_qa_dataset.jsonl | 问答对格式 |
| simple_translate.py | translated_qa_dataset.jsonl | 问答对格式 |

## 📋 输出格式示例

每个翻译结果都是一个问答对：

```json
{
  "text": "Human:\n请将以下英文翻译成中文：\nCreate a Python script snippet.\n\nAssistant:\n创建一个Python脚本片段。",
  "metadata": {
    "original_entry_id": 1,
    "sentence_id": 1,
    "original_text": "Create a Python script snippet.",
    "translated_text": "创建一个Python脚本片段。",
    "translation_mode": "offline",
    "source": "enhanced_translation"
  }
}
```

## ⚙️ 常用参数

### enhanced_translate.py 参数
- `--mode`: 翻译模式 (offline/online/hybrid)
- `--max-entries`: 最大处理条目数
- `--input`: 输入文件路径
- `--output`: 输出文件路径

### 示例命令
```bash
# 处理100个条目，使用混合模式
python enhanced_translate.py --mode hybrid --max-entries 100

# 指定输入输出文件
python enhanced_translate.py --input my_data.jsonl --output my_result.jsonl --mode offline
```

## 🔧 故障排除

### 问题1：找不到输入文件
```
错误: 找不到输入文件 english_data.jsonl
```
**解决方案**：确保 `english_data.jsonl` 文件在当前目录下，或使用 `--input` 参数指定正确路径。

### 问题2：网络连接错误
```
翻译错误: HTTPSConnectionPool(host='translate.googleapis.com'...
```
**解决方案**：使用离线模式 `--mode offline` 或检查网络连接。

### 问题3：翻译质量不满意
**解决方案**：
- 离线模式翻译质量有限，可尝试在线模式
- 可以手动编辑词典文件改善翻译
- 使用混合模式获得更好效果

## 📊 性能参考

| 模式 | 处理速度 | 翻译质量 | 网络需求 |
|------|---------|---------|---------|
| offline | 很快 | 一般 | 无 |
| online | 慢 | 好 | 需要 |
| hybrid | 中等 | 较好 | 可选 |

## 💡 使用建议

1. **首次使用**：建议先用 `--max-entries 5` 测试
2. **大批量处理**：使用离线模式避免API限制
3. **高质量需求**：使用在线模式或混合模式
4. **网络受限**：使用离线模式

## 📝 下一步

1. 运行脚本生成翻译数据
2. 检查输出文件质量
3. 根据需要调整参数重新运行
4. 将生成的问答对用于你的项目

## 🆘 获取帮助

如果遇到问题：
1. 查看详细的 `翻译脚本使用说明.md`
2. 检查脚本中的注释
3. 尝试不同的参数组合
