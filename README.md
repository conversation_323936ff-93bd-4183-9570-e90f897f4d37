# 中英文数据集分离工具

## 概述

这个工具用于将混合的中英文JSONL数据集按语言分离为三个独立的文件：纯中文、纯英文和中英混合。

## 文件说明

- `separate_languages.py` - 主要的分离脚本
- `verify_separation.py` - 验证分离结果的脚本
- `001_00001.jsonl` - 原始数据集文件
- `chinese_data.jsonl` - 分离出的纯中文数据
- `english_data.jsonl` - 分离出的纯英文数据
- `mixed_data.jsonl` - 分离出的中英混合数据

## 分离规则

1. **中文检测**: 使用Unicode范围 `[\u4e00-\u9fff]` 检测中文字符
2. **词汇计数**:
   - 中文词汇：按中文字符数量计算
   - 英文词汇：使用正则表达式 `\b[a-zA-Z]+\b` 匹配英文单词
3. **三分类逻辑**:
   - **混合内容**: 中文词汇 ≥ 3 且 英文词汇 ≥ 3
   - **中文内容**: 中文词汇 ≥ 3 或包含中文字符
   - **英文内容**: 其他情况

## 分离结果

### 原始数据统计
- **总条目数**: 25,492

### 分离后统计
- **纯中文条目数**: 1,898 (7.45%)
- **纯英文条目数**: 21,546 (84.52%)
- **中英混合条目数**: 2,048 (8.03%)

### 分类准确率
- **中文文件准确率**: 100.00% (所有条目都是纯中文)
- **英文文件准确率**: 100.00% (所有条目都是纯英文)
- **混合文件准确率**: 100.00% (所有条目都包含中英混合内容)

## 使用方法

### 1. 运行分离脚本
```bash
python separate_languages.py
```

### 2. 验证分离结果
```bash
python verify_separation.py
```

## 脚本特性

### separate_languages.py
- 支持大文件处理（显示进度）
- 错误处理和日志记录
- 自动创建输出目录
- 保持原始JSON格式
- 支持UTF-8编码

### verify_separation.py
- 详细的统计分析
- 分类准确率计算
- 样本数据预览
- 语言分布分析

## 技术细节

### 中文字符检测
```python
def contains_chinese(text: str) -> bool:
    chinese_pattern = re.compile(r'[\u4e00-\u9fff]')
    return bool(chinese_pattern.search(text))
```

### 词汇计数
```python
def count_english_words(text: str) -> int:
    english_words = re.findall(r'\b[a-zA-Z]+\b', text)
    return len(english_words)

def count_chinese_words(text: str) -> int:
    chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
    return len(chinese_chars)
```

### 三分类逻辑
```python
def classify_entry(entry: Dict[str, Any]) -> str:
    text_content = entry.get('text', '')
    chinese_word_count = count_chinese_words(text_content)
    english_word_count = count_english_words(text_content)

    has_significant_chinese = chinese_word_count >= 3
    has_significant_english = english_word_count >= 3

    if has_significant_chinese and has_significant_english:
        return 'mixed'
    elif has_significant_chinese or (contains_chinese(text_content) and chinese_word_count > 0):
        return 'chinese'
    else:
        return 'english'
```

## 输出文件格式

输出文件保持原始JSONL格式：
```json
{"text": "对话内容...", "metadata": {"loss": 0.123, "toxicity": 0.001}}
```

### 分类示例

**纯中文条目**:
```json
{"text": "Human:\n鉴别两种风格迥异的艺术品，分析其不同之处。\n\nAssistant:\n1. 风格的不同：两种艺术品的风格可以迥然不同..."}
```

**纯英文条目**:
```json
{"text": "Human:\nCreate a Python script snippet that Updates Low Bathing routine...\n\nAssistant:\nHere is python code which updates..."}
```

**中英混合条目**:
```json
{"text": "Human:\n请用Python写一个function来calculate the average...\n\nAssistant:\n当然可以。例如，使用Python Pandas库中的df.loc[]方法..."}
```

## 注意事项

1. 脚本假设输入文件为UTF-8编码
2. 每行必须是有效的JSON格式
3. 分离过程中会跳过无效的JSON行并记录错误
4. 输出文件会覆盖同名的现有文件

## 系统要求

- Python 3.6+
- 标准库模块：json, re, os, typing

## 许可证

此工具仅供学习和研究使用。
