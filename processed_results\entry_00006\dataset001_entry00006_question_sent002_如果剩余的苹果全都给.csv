﻿Text,tok/fine,tok/coarse,pos/ctb,pos/863,pos/pku,ner/msra,ner/pku,ner/ontonotes,srl,dep,sdp,con
如果剩余的苹果全都给小明吃，他还能吃几个苹果,"['如果', '剩余', '的', '苹果', '全', '都', '给', '小明', '吃', '，', '他', '还', '能', '吃', '几', '个', '苹果']","['如果', '剩余', '的', '苹果', '全都', '给', '小明', '吃', '，', '他', '还', '能', '吃', '几', '个', '苹果']","['CS', 'VV', 'DEC', 'NN', 'AD', 'AD', 'VV', 'NR', 'VV', 'PU', 'PN', 'AD', 'VV', 'VV', 'CD', 'M', 'NN']","['c', 'v', 'u', 'n', 'a', 'd', 'p', 'nhs', 'v', 'w', 'r', 'd', 'vu', 'v', 'm', 'q', 'n']","['c', 'v', 'u', 'n', 'd', 'd', 'v', 'nr', 'v', 'w', 'r', 'd', 'v', 'v', 'm', 'q', 'n']","[('小明', 'PERSON', 7, 8)]","[('小明', 'nr', 7, 8)]",[],"[[('剩余的苹果', 'ARG1', 1, 4), ('全', 'ARGM-ADV', 4, 5), ('都', 'ARGM-ADV', 5, 6), ('给', 'PRED', 6, 7), ('小明', 'ARG2', 7, 8)]]","[(7, 'advmod'), (4, 'rcmod'), (2, 'cpm'), (7, 'nsubj'), (7, 'advmod'), (7, 'advmod'), (14, 'pccomp'), (7, 'dobj'), (7, 'dep'), (14, 'punct'), (14, 'nsubj'), (14, 'advmod'), (14, 'mmod'), (0, 'root'), (16, 'nummod'), (17, 'clf'), (14, 'dobj')]","[[(7, 'mConj')], [(4, 'rPat')], [(2, 'mAux')], [(7, 'Pat'), (9, 'Pat')], [(7, 'mRang')], [(7, 'mRang')], [(14, 'eSupp')], [(7, 'Datv'), (9, 'Agt')], [(7, 'ePurp')], [(9, 'mPunc')], [(14, 'Agt')], [(14, 'mMod')], [(14, 'mMod')], [(0, 'Root')], [(16, 'Quan')], [(17, 'Qp')], [(14, 'Cont')]]","(TOP
  (IP
    (CP
      (ADVP (CS 如果))
      (IP
        (NP (CP (CP (IP (VP (VV 剩余))) (DEC 的))) (NP (NN 苹果)))
        (VP
          (ADVP (AD 全))
          (ADVP (AD 都))
          (VP (VV 给) (NP (NR 小明)) (IP (VP (VV 吃)))))))
    (PU ，)
    (NP (PN 他))
    (VP
      (ADVP (AD 还))
      (VP
        (VV 能)
        (VP (VV 吃) (NP (QP (CD 几) (CLP (M 个))) (NP (NN 苹果))))))))"
